import express, { Request, Response, NextFunction } from "express";
import { prisma } from "../utils/database";
import { authenticate, authorize, AuthRequest } from "../middleware/auth";
import { getCache, setCache } from "../utils/redis";
import logger from "../utils/logger";

const router = express.Router();

// GET /api/analytics/dashboard - Get dashboard statistics
router.get(
  "/dashboard",
  [authenticate, authorize("admin")],
  async (
    req: AuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      // Check cache
      const cacheKey = "analytics:dashboard";
      const cached = await getCache(cacheKey);
      if (cached) {
        res.json(cached);
        return;
      }

      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      // Get all statistics in parallel
      const [
        totalPanoramas,
        totalHotspots,
        totalLanguages,
        activeLanguages,
        viewsToday,
        recentActivities,
      ] = await Promise.all([
        // Total panoramas
        prisma.panorama.count(),

        // Total hotspots
        prisma.hotspot.count(),

        // Total languages
        prisma.language.count(),

        // Active languages
        prisma.language.count({
          where: { isActive: true },
        }),

        // Views today
        prisma.panoramaView.count({
          where: {
            viewedAt: {
              gte: today,
              lt: tomorrow,
            },
          },
        }),

        // Recent activities (last 10)
        prisma.activity.findMany({
          take: 10,
          orderBy: { createdAt: "desc" },
          include: {
            panorama: {
              select: {
                id: true,
                title: true,
              },
            },
          },
        }),
      ]);

      const result = {
        success: true,
        data: {
          stats: {
            panoramas: totalPanoramas,
            hotspots: totalHotspots,
            languages: totalLanguages,
            activeLanguages,
            viewsToday,
          },
          recentActivities,
        },
      };

      // Cache for 5 minutes
      await setCache(cacheKey, result, 300);

      res.json(result);
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/analytics/view - Track a panorama view (public endpoint)
router.post(
  "/view",
  async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { panoramaId } = req.body;

      if (!panoramaId) {
        res.status(400).json({
          success: false,
          error: { message: "Panorama ID is required" },
        });
        return;
      }

      // Verify panorama exists
      const panorama = await prisma.panorama.findUnique({
        where: { id: panoramaId },
      });

      if (!panorama) {
        res.status(404).json({
          success: false,
          error: { message: "Panorama not found" },
        });
        return;
      }

      // Get client information
      const ipAddress = req.ip || req.connection.remoteAddress || "unknown";
      const userAgent = req.headers["user-agent"] || "unknown";
      const referrerHeader = req.headers.referer || req.headers.referrer;
      const referrer = Array.isArray(referrerHeader)
        ? referrerHeader[0]
        : referrerHeader || null;

      // Create view record
      await prisma.panoramaView.create({
        data: {
          panoramaId,
          ipAddress,
          userAgent,
          referrer,
        },
      });

      // Clear dashboard cache to update stats
      await setCache("analytics:dashboard", null, 0);

      res.json({
        success: true,
        message: "View tracked successfully",
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/analytics/activity - Log an activity (internal use)
router.post(
  "/activity",
  [authenticate, authorize("admin")],
  async (
    req: AuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const {
        type,
        title,
        description,
        entityType,
        entityId,
        panoramaId,
        metadata,
      } = req.body;

      if (!type || !title) {
        res.status(400).json({
          success: false,
          error: { message: "Type and title are required" },
        });
        return;
      }

      const activity = await prisma.activity.create({
        data: {
          type,
          title,
          description,
          entityType,
          entityId,
          panoramaId,
          userId: req.user?.id,
          metadata,
        },
      });

      // Clear dashboard cache to update activities
      await setCache("analytics:dashboard", null, 0);

      res.json({
        success: true,
        data: { activity },
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/analytics/views/:panoramaId - Get views for a specific panorama
router.get(
  "/views/:panoramaId",
  [authenticate, authorize("admin")],
  async (
    req: AuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { panoramaId } = req.params;
      const { days = 7 } = req.query;

      const startDate = new Date();
      startDate.setDate(startDate.getDate() - Number(days));

      const views = await prisma.panoramaView.findMany({
        where: {
          panoramaId,
          viewedAt: {
            gte: startDate,
          },
        },
        orderBy: { viewedAt: "desc" },
      });

      // Group by date
      const viewsByDate = views.reduce((acc, view) => {
        const date = view.viewedAt.toISOString().split("T")[0];
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      res.json({
        success: true,
        data: {
          totalViews: views.length,
          viewsByDate,
          views,
        },
      });
    } catch (error) {
      next(error);
    }
  }
);

export default router;
