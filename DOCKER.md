# Docker Setup for IduView

This document explains how to build and deploy IduView using Docker.

## Prerequisites

- Docker
- Docker Compose
- Git

## Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/TimoLambing/iduview.git
   cd iduview
   ```

2. **Create environment file**
   ```bash
   cp .env.example .env.production
   # Edit .env.production with your values
   ```

3. **Deploy using the script**
   ```bash
   ./deploy.sh
   ```

## Manual Docker Commands

### Build Individual Containers

**Backend:**
```bash
cd backend
docker build -t iduview-backend .
```

**Frontend:**
```bash
cd frontend
docker build -t iduview-frontend .
```

### Using Docker Compose

**Start all services:**
```bash
docker-compose up -d
```

**Build and start:**
```bash
docker-compose up --build -d
```

**Stop services:**
```bash
docker-compose down
```

**View logs:**
```bash
docker-compose logs -f
```

**Restart specific service:**
```bash
docker-compose restart backend
```

## Services

The docker-compose setup includes:

- **Frontend** (Next.js) - Port 3000
- **Backend** (Node.js/Express) - Port 5000
- **Redis** (Cache) - Port 6379
- **Nginx** (Reverse Proxy) - Ports 80/443

## Environment Variables

Create `.env.production` with:

```env
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/iduview

# JWT
JWT_SECRET=your-super-secret-jwt-key

# API URLs
API_BASE_URL=http://localhost:5000
FRONTEND_BASE_URL=http://localhost:3000

# Redis (Production)
REDIS_URL=redis://localhost:6379
```

## GitHub Actions Deployment

The repository includes a GitHub Actions workflow that:

1. Builds both frontend and backend
2. Runs tests
3. Deploys to your server using SSH

### Required Secrets

Add these secrets to your GitHub repository:

- `HOST` - Your server IP/domain
- `USERNAME` - SSH username
- `SSH_KEY` - Private SSH key
- `PORT` - SSH port (usually 22)

## Troubleshooting

**Container won't start:**
```bash
docker-compose logs [service-name]
```

**Database connection issues:**
```bash
docker-compose exec backend npx prisma migrate deploy
```

**Clear everything and restart:**
```bash
docker-compose down --volumes --rmi all
docker-compose up --build -d
```

## Production Considerations

1. **SSL Certificates** - Configure nginx with proper SSL
2. **Environment Variables** - Use secure values in production
3. **Database Backups** - Set up regular PostgreSQL backups
4. **Monitoring** - Add health checks and monitoring
5. **Scaling** - Consider using Docker Swarm or Kubernetes for scaling
