#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

// Set environment variables explicitly
process.env.NODE_ENV = 'development';
process.env.DATABASE_URL = 'postgresql://root:Onamission%23007@************:5432/iduna_db';
process.env.NEXT_PUBLIC_API_URL = 'http://localhost:5000';
process.env.NEXT_PUBLIC_FRONTEND_URL = 'http://localhost:3000';
process.env.JWT_SECRET = 'dev_jwt_secret_change_in_production_2024';

console.log('🚀 Starting IduView development servers with environment...\n');

// Start backend
console.log('📡 Starting backend server...');
const backend = spawn('npm', ['run', 'dev'], {
  cwd: path.join(__dirname, 'backend'),
  stdio: 'inherit',
  shell: true,
  env: { ...process.env }
});

// Wait a bit for backend to start
setTimeout(() => {
  console.log('🌐 Starting frontend server...');
  const frontend = spawn('npm', ['run', 'dev'], {
    cwd: path.join(__dirname, 'frontend'),
    stdio: 'inherit',
    shell: true,
    env: { ...process.env }
  });

  // Handle process termination
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down servers...');
    backend.kill('SIGINT');
    frontend.kill('SIGINT');
    process.exit(0);
  });

  frontend.on('close', (code) => {
    console.log(`Frontend process exited with code ${code}`);
    backend.kill('SIGINT');
    process.exit(code);
  });

  backend.on('close', (code) => {
    console.log(`Backend process exited with code ${code}`);
    frontend.kill('SIGINT');
    process.exit(code);
  });
}, 3000);

backend.on('close', (code) => {
  console.log(`Backend process exited with code ${code}`);
  process.exit(code);
});
