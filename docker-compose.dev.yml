version: '3.8'

services:
  frontend-dev:
    build: 
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: iduview-frontend-dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=${API_BASE_URL}
      - NEXT_PUBLIC_FRONTEND_URL=${FRONTEND_BASE_URL}
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend-dev
    networks:
      - iduview-dev-network

  backend-dev:
    build: 
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: iduview-backend-dev
    ports:
      - "5000:5000"
    env_file:
      - .env.development
    volumes:
      - ./backend:/app
      - /app/node_modules
      - ./backend/uploads:/app/uploads
    networks:
      - iduview-dev-network

networks:
  iduview-dev-network:
    driver: bridge
