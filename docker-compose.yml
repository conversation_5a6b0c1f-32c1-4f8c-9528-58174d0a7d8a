version: "3.8"

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: iduview-frontend
    restart: unless-stopped
    env_file:
      - .env.docker
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://37.27.84.251:5000
      - NEXT_PUBLIC_FRONTEND_URL=http://37.27.84.251:3000
    ports:
      - "3000:3000"
    volumes:
      - ./frontend/public/panoramas:/app/public/panoramas
    depends_on:
      - backend
    networks:
      - iduview-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: iduview-backend
    restart: unless-stopped
    env_file:
      - .env.docker
    ports:
      - "5000:5000"
    volumes:
      - backend_uploads:/app/uploads
      - ./shared-assets:/app/shared-assets
    depends_on:
      - redis
    networks:
      - iduview-network

  redis:
    image: redis:7-alpine
    container_name: iduview-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    networks:
      - iduview-network

volumes:
  redis_data:
  backend_uploads:

networks:
  iduview-network:
    driver: bridge
