// frontend\src\pages\admin\panoramas.tsx

import React, { useState, useEffect } from "react";
import AdminLayout from "@/components/AdminLayout";
import axios from "axios";
import { toast } from "react-hot-toast";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  Upload,
  Image as ImageIcon,
  MapPin,
  Calendar,
  FileImage,
  Code,
  ExternalLink,
  Copy,
  Target,
} from "lucide-react";
import { FileUpload } from "@/components/ui/file-upload";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAppTranslation } from "@/hooks/useAppTranslation";

interface Panorama {
  id: string;
  title: string;
  description?: string;
  imageUrl: string;
  thumbnailPath?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  _count: {
    hotspots: number;
  };
}

// We'll create the schema inside the component to access translations

type PanoramaFormData = {
  title: string;
  description?: string;
  imageUrl?: string;
};

const PanoramasPage: React.FC = () => {
  const { t } = useAppTranslation();
  const [panoramas, setPanoramas] = useState<Panorama[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingPanorama, setEditingPanorama] = useState<Panorama | null>(null);
  const [uploadMethod, setUploadMethod] = useState<"file" | "url">("file");
  const [showIframeDialog, setShowIframeDialog] = useState(false);
  const [selectedPanoramaForIframe, setSelectedPanoramaForIframe] =
    useState<Panorama | null>(null);
  const [uploadedFileData, setUploadedFileData] = useState<any>(null);

  // Create schema with translations
  const panoramaSchema = yup.object({
    title: yup
      .string()
      .required(
        t(
          "admin.panoramasPage.form.validation.titleRequired",
          "Title is required"
        )
      ),
    description: yup.string(),
    imageUrl: yup
      .string()
      .url(
        t(
          "admin.panoramasPage.form.validation.urlInvalid",
          "Must be a valid URL"
        )
      )
      .optional(),
  });

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<PanoramaFormData>({
    resolver: yupResolver(panoramaSchema),
    defaultValues: {
      title: "",
      description: undefined,
      imageUrl: undefined,
    },
  });

  useEffect(() => {
    fetchPanoramas();
  }, []);

  const fetchPanoramas = async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_API_URL}/api/panoramas`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      setPanoramas(response.data.data.panoramas || []);
    } catch (error) {
      toast.error(
        t(
          "admin.panoramasPage.messages.fetchFailed",
          "Failed to fetch panoramas"
        )
      );
      // Set empty array as fallback
      setPanoramas([]);
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = (url: string, uploadData?: any) => {
    console.log("📁 File upload completed, URL:", url);
    console.log("📊 Upload data:", uploadData);
    setValue("imageUrl", url);
    setUploadedFileData(uploadData);
    console.log("✅ imageUrl set in form and upload data stored");
    toast.success(
      t(
        "admin.panoramasPage.messages.uploadSuccess",
        "File uploaded successfully!"
      )
    );
  };

  const onSubmit = async (data: PanoramaFormData) => {
    console.log("🚀 Form submission started");
    console.log("📝 Form data:", data);
    console.log("🔍 imageUrl value:", data.imageUrl);
    console.log("📊 Upload data:", uploadedFileData);

    // Validate that we have either a file upload or URL (only for new panoramas)
    if (!editingPanorama && !data.imageUrl) {
      console.log("❌ No imageUrl provided for new panorama");
      toast.error(
        t(
          "admin.panoramasPage.form.validation.imageRequired",
          "Please upload a file or provide an image URL"
        )
      );
      return;
    }

    console.log("✅ Validation passed, proceeding with API call");

    try {
      const token = localStorage.getItem("token");
      const headers = { Authorization: `Bearer ${token}` };

      console.log("🔑 Token:", token ? "Present" : "Missing");
      console.log("📡 API URL:", process.env.NEXT_PUBLIC_API_URL);

      if (editingPanorama) {
        console.log("✏️ Updating existing panorama");

        // For updates, only send title, description, and isActive
        // Don't send imageUrl/filename unless user uploaded a new file
        const updateData: any = {
          title: data.title,
          description: data.description || "",
        };

        // If user uploaded a new file, we need to handle it differently
        if (uploadedFileData && uploadMethod === "file") {
          // Show confirmation dialog for new image upload
          const confirmUpload = confirm(
            t(
              "admin.panoramasPage.messages.uploadWarning",
              "Uploading a new picture will result in a loss of the hotspots of this panorama picture! Are you sure?"
            )
          );

          if (!confirmUpload) {
            return; // User cancelled
          }

          // Handle new image upload for existing panorama
          updateData.filename =
            uploadedFileData.optimized?.filename ||
            uploadedFileData.original?.filename;
          updateData.width =
            uploadedFileData.optimized?.width ||
            uploadedFileData.original?.width;
          updateData.height =
            uploadedFileData.optimized?.height ||
            uploadedFileData.original?.height;
          updateData.fileSize =
            uploadedFileData.optimized?.size || uploadedFileData.original?.size;
          updateData.mimeType =
            uploadedFileData.optimized?.mimeType ||
            uploadedFileData.original?.mimeType;
          updateData.thumbnailPath = uploadedFileData.thumbnail?.path;
          updateData.replaceImage = true; // Flag to indicate image replacement
        }

        await axios.put(
          `${process.env.NEXT_PUBLIC_API_URL}/api/panoramas/${editingPanorama.id}`,
          updateData,
          { headers }
        );
        toast.success(
          t(
            "admin.panoramasPage.messages.updateSuccess",
            "Panorama updated successfully"
          )
        );
      } else {
        console.log("➕ Creating new panorama");

        // Prepare data for backend - use upload metadata if available
        let requestData;
        if (uploadedFileData && uploadMethod === "file") {
          requestData = {
            title: data.title,
            description: data.description || "",
            filename:
              uploadedFileData.optimized?.filename ||
              uploadedFileData.original?.filename,
            width:
              uploadedFileData.optimized?.width ||
              uploadedFileData.original?.width,
            height:
              uploadedFileData.optimized?.height ||
              uploadedFileData.original?.height,
            fileSize:
              uploadedFileData.optimized?.size ||
              uploadedFileData.original?.size,
            mimeType:
              uploadedFileData.optimized?.mimeType ||
              uploadedFileData.original?.mimeType,
            thumbnailPath: uploadedFileData.thumbnail?.path,
          };
        } else {
          // For URL method, we need to get file info differently
          requestData = {
            title: data.title,
            description: data.description || "",
            filename: data.imageUrl?.split("/").pop() || "unknown.jpg",
            width: 4096, // Default values for URL method
            height: 2048,
            fileSize: 1000000, // Default 1MB for URL method (backend requires min: 1)
            mimeType: "image/jpeg",
            // No thumbnail for URL method
          };
        }

        console.log("📤 Request data:", requestData);

        const response = await axios.post(
          `${process.env.NEXT_PUBLIC_API_URL}/api/panoramas`,
          requestData,
          { headers }
        );
        console.log("✅ Create response:", response.data);
        toast.success(
          t(
            "admin.panoramasPage.messages.createSuccess",
            "Panorama created successfully"
          )
        );
      }

      console.log("🔄 Refreshing panorama list");
      fetchPanoramas();
      handleCloseForm();
    } catch (error: any) {
      console.error("❌ Form submission error:", error);
      console.error("📄 Error response:", error.response?.data);
      const message =
        error.response?.data?.error?.message || "Operation failed";
      toast.error(message);
    }
  };

  const handleEdit = (panorama: Panorama) => {
    setEditingPanorama(panorama);
    setValue("title", panorama.title);
    setValue("description", panorama.description || "");
    // Don't set imageUrl for editing - we'll show the current image as preview
    setShowForm(true);
  };

  const handleDelete = async (id: string) => {
    if (
      !confirm(
        t(
          "admin.panoramasPage.messages.deleteConfirm",
          "Are you sure you want to delete this panorama?"
        )
      )
    )
      return;

    try {
      const token = localStorage.getItem("token");
      await axios.delete(
        `${process.env.NEXT_PUBLIC_API_URL}/api/panoramas/${id}`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      toast.success(
        t(
          "admin.panoramasPage.messages.deleteSuccess",
          "Panorama deleted successfully"
        )
      );
      fetchPanoramas();
    } catch (error: any) {
      const message = error.response?.data?.error?.message || "Delete failed";
      toast.error(message);
    }
  };

  const toggleActive = async (id: string, isActive: boolean) => {
    try {
      const token = localStorage.getItem("token");
      await axios.put(
        `${process.env.NEXT_PUBLIC_API_URL}/api/panoramas/${id}`,
        { isActive: !isActive },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      const statusText = !isActive
        ? t("admin.panoramasPage.messages.activated", "activated")
        : t("admin.panoramasPage.messages.deactivated", "deactivated");
      toast.success(`Panorama ${statusText}`);
      fetchPanoramas();
    } catch (error: any) {
      const message = error.response?.data?.error?.message || "Update failed";
      toast.error(message);
    }
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setEditingPanorama(null);
    setUploadMethod("file");
    setUploadedFileData(null);
    reset();
  };

  const handleShowIframe = (panorama: Panorama) => {
    setSelectedPanoramaForIframe(panorama);
    setShowIframeDialog(true);
  };

  const generateIframeCode = (
    panorama: Panorama,
    width = 800,
    height = 600
  ) => {
    const baseUrl = typeof window !== "undefined" ? window.location.origin : "";
    const embedUrl = `${baseUrl}/embed/${panorama.id}`;

    return `<iframe
  src="${embedUrl}"
  width="${width}"
  height="${height}"
  frameborder="0"
  allowfullscreen
  title="${panorama.title} - 360° Panorama">
</iframe>`;
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(
        t("admin.panoramasPage.messages.copySuccess", "Copied to clipboard!")
      );
    } catch (error) {
      toast.error(
        t(
          "admin.panoramasPage.messages.copyFailed",
          "Failed to copy to clipboard"
        )
      );
    }
  };

  const stats = {
    total: panoramas.length,
    active: panoramas.filter((p) => p.isActive).length,
    totalHotspots: panoramas.reduce((sum, p) => sum + p._count.hotspots, 0),
  };

  if (loading) {
    return (
      <AdminLayout title={t("admin.panoramasPage.title", "Panoramas")}>
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="text-sm text-muted-foreground">
              {t("admin.panoramasPage.loading", "Loading panoramas...")}
            </p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title={t("admin.panoramasPage.title", "Panoramas")}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col space-y-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div>
              <h2 className="text-2xl sm:text-3xl font-bold tracking-tight">
                {t("admin.panoramasPage.title", "Panoramas")}
              </h2>
              <p className="text-muted-foreground text-sm sm:text-base">
                {t(
                  "admin.panoramasPage.subtitle",
                  "Manage your 360° panorama collection"
                )}
              </p>
            </div>
            <Dialog open={showForm} onOpenChange={setShowForm}>
              <DialogTrigger asChild>
                <Button className="w-full sm:w-auto">
                  <Plus className="mr-2 h-4 w-4" />
                  <span className="sm:inline">
                    {t("admin.panoramasPage.addPanorama", "Add Panorama")}
                  </span>
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-[calc(100vw-24px)] sm:max-w-[600px] max-h-[90vh] overflow-y-auto rounded-lg">
                <form onSubmit={handleSubmit(onSubmit as any)}>
                  <DialogHeader>
                    <DialogTitle>
                      {editingPanorama
                        ? t(
                            "admin.panoramasPage.form.editTitle",
                            "Edit Panorama"
                          )
                        : t(
                            "admin.panoramasPage.form.addTitle",
                            "Add Panorama"
                          )}
                    </DialogTitle>
                    <DialogDescription>
                      {editingPanorama
                        ? t(
                            "admin.panoramasPage.form.editDescription",
                            "Update the panorama details below."
                          )
                        : t(
                            "admin.panoramasPage.form.addDescription",
                            "Add a new 360° panorama to your collection."
                          )}
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="title">
                        {t("admin.panoramasPage.form.titleLabel", "Title")}
                      </Label>
                      <Input
                        id="title"
                        placeholder={t(
                          "admin.panoramasPage.form.titlePlaceholder",
                          "e.g., Modern Kitchen 360°"
                        )}
                        {...register("title")}
                      />
                      {errors.title && (
                        <p className="text-sm text-destructive">
                          {errors.title.message}
                        </p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">
                        {t(
                          "admin.panoramasPage.form.descriptionLabel",
                          "Description"
                        )}
                      </Label>
                      <Textarea
                        id="description"
                        placeholder={t(
                          "admin.panoramasPage.form.descriptionPlaceholder",
                          "Describe this panorama..."
                        )}
                        {...register("description")}
                      />
                      {errors.description && (
                        <p className="text-sm text-destructive">
                          {errors.description.message}
                        </p>
                      )}
                    </div>

                    {/* Image Upload Section */}
                    <div className="space-y-2">
                      <Label>
                        {t(
                          "admin.panoramasPage.form.imageLabel",
                          "Panorama Image"
                        )}
                      </Label>
                      <Tabs
                        value={uploadMethod}
                        onValueChange={(value) =>
                          setUploadMethod(value as "file" | "url")
                        }
                      >
                        <TabsList className="grid w-full grid-cols-2">
                          <TabsTrigger value="file">
                            {t(
                              "admin.panoramasPage.form.uploadFile",
                              "Upload File"
                            )}
                          </TabsTrigger>
                          <TabsTrigger value="url">
                            {t(
                              "admin.panoramasPage.form.imageUrl",
                              "Image URL"
                            )}
                          </TabsTrigger>
                        </TabsList>
                        <TabsContent value="file" className="space-y-2">
                          <FileUpload
                            onFileSelect={(file) => {
                              // File selected, will be uploaded automatically
                            }}
                            onFileUpload={handleFileUpload}
                            accept="image/*"
                            maxSize={50}
                            uploadEndpoint="/api/upload/panorama"
                            preview={true}
                          />
                        </TabsContent>
                        <TabsContent value="url" className="space-y-2">
                          <Input
                            id="imageUrl"
                            placeholder={t(
                              "admin.panoramasPage.form.urlPlaceholder",
                              "/panoramas/kitchen.jpg or https://example.com/image.jpg"
                            )}
                            {...register("imageUrl")}
                          />
                          {errors.imageUrl && (
                            <p className="text-sm text-destructive">
                              {errors.imageUrl.message}
                            </p>
                          )}
                        </TabsContent>
                      </Tabs>
                    </div>
                  </div>
                  <DialogFooter className="flex flex-col sm:flex-row gap-3 sm:gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleCloseForm}
                      className="w-full sm:w-auto"
                    >
                      {t("admin.panoramasPage.form.cancel", "Cancel")}
                    </Button>
                    <Button
                      type="button"
                      disabled={isSubmitting}
                      className="w-full sm:w-auto"
                      onClick={async () => {
                        console.log("🖱️ Create button clicked");

                        // Get current form values
                        const formElement = document.querySelector("form");
                        if (formElement) {
                          const formData = new FormData(formElement);
                          let imageUrl = formData.get("imageUrl") as string;

                          // For file upload method, use the stored upload data
                          if (uploadMethod === "file" && uploadedFileData) {
                            imageUrl =
                              uploadedFileData.optimized?.path ||
                              uploadedFileData.original?.path;
                          }

                          const data = {
                            title: formData.get("title") as string,
                            description: formData.get("description") as string,
                            imageUrl: imageUrl,
                          };
                          console.log("📋 Form data collected:", data);
                          await onSubmit(data);
                        }
                      }}
                    >
                      {isSubmitting
                        ? t("admin.panoramasPage.form.saving", "Saving...")
                        : editingPanorama
                        ? t("admin.panoramasPage.form.update", "Update")
                        : t("admin.panoramasPage.form.create", "Create")}
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-3">
          <Card className="border-border/50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t(
                  "admin.panoramasPage.stats.totalPanoramas",
                  "Total Panoramas"
                )}
              </CardTitle>
              <ImageIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
              <p className="text-xs text-muted-foreground">
                {stats.active} {t("admin.panoramasPage.stats.active", "active")}
              </p>
            </CardContent>
          </Card>
          <Card className="border-border/50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t(
                  "admin.panoramasPage.stats.activePanoramas",
                  "Active Panoramas"
                )}
              </CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.active}</div>
              <p className="text-xs text-muted-foreground">
                {t(
                  "admin.panoramasPage.stats.availableForViewing",
                  "Available for viewing"
                )}
              </p>
            </CardContent>
          </Card>
          <Card className="border-border/50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t("admin.panoramasPage.stats.totalHotspots", "Total Hotspots")}
              </CardTitle>
              <MapPin className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalHotspots}</div>
              <p className="text-xs text-muted-foreground">
                {t(
                  "admin.panoramasPage.stats.interactivePoints",
                  "Interactive points"
                )}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Panoramas Management */}
        <Card>
          <CardHeader>
            <CardTitle>
              {t("admin.panoramasPage.table.title", "Panorama Management")}
            </CardTitle>
            <CardDescription>
              {t(
                "admin.panoramasPage.table.subtitle",
                "Configure and manage your 360° panorama collection"
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {panoramas.length === 0 ? (
              <div className="text-center py-8">
                <FileImage className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="mt-2 text-sm font-semibold">
                  {t("admin.panoramasPage.table.noPanoramas", "No panoramas")}
                </h3>
                <p className="mt-1 text-sm text-muted-foreground">
                  {t(
                    "admin.panoramasPage.table.noPanoramasDesc",
                    "Get started by adding your first 360° panorama."
                  )}
                </p>
                <div className="mt-6">
                  <Button onClick={() => setShowForm(true)}>
                    <Plus className="mr-2 h-4 w-4" />
                    {t("admin.panoramasPage.addPanorama", "Add Panorama")}
                  </Button>
                </div>
              </div>
            ) : (
              <>
                {/* Desktop Table View */}
                <div className="hidden md:block">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>
                          {t(
                            "admin.panoramasPage.table.headers.panorama",
                            "Panorama"
                          )}
                        </TableHead>
                        <TableHead>
                          {t(
                            "admin.panoramasPage.table.headers.hotspots",
                            "Hotspots"
                          )}
                        </TableHead>
                        <TableHead>
                          {t(
                            "admin.panoramasPage.table.headers.status",
                            "Status"
                          )}
                        </TableHead>
                        <TableHead>
                          {t(
                            "admin.panoramasPage.table.headers.created",
                            "Created"
                          )}
                        </TableHead>
                        <TableHead>
                          {t(
                            "admin.panoramasPage.table.headers.active",
                            "Active"
                          )}
                        </TableHead>
                        <TableHead className="text-right">
                          {t(
                            "admin.panoramasPage.table.headers.actions",
                            "Actions"
                          )}
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {panoramas.map((panorama) => (
                        <TableRow key={panorama.id}>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <div className="w-16 h-10 bg-muted rounded-lg overflow-hidden flex items-center justify-center">
                                {panorama.thumbnailPath ? (
                                  <img
                                    src={`${
                                      process.env.NEXT_PUBLIC_API_URL ||
                                      "http://localhost:5000"
                                    }${panorama.thumbnailPath}`}
                                    alt={panorama.title}
                                    className="w-full h-full object-cover"
                                    onError={(e) => {
                                      // Fallback to icon if thumbnail fails to load
                                      e.currentTarget.style.display = "none";
                                      e.currentTarget.nextElementSibling?.classList.remove(
                                        "hidden"
                                      );
                                    }}
                                  />
                                ) : null}
                                <ImageIcon
                                  className={`h-5 w-5 text-muted-foreground ${
                                    panorama.thumbnailPath ? "hidden" : ""
                                  }`}
                                />
                              </div>
                              <div>
                                <div className="font-medium">
                                  {panorama.title}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {panorama.description ||
                                    t(
                                      "admin.panoramasPage.table.noDescription",
                                      "No description"
                                    )}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-1">
                              <MapPin className="h-4 w-4 text-muted-foreground" />
                              <span>{panorama._count.hotspots}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                panorama.isActive ? "default" : "secondary"
                              }
                            >
                              {panorama.isActive
                                ? t(
                                    "admin.panoramasPage.table.statusActive",
                                    "Active"
                                  )
                                : t(
                                    "admin.panoramasPage.table.statusInactive",
                                    "Inactive"
                                  )}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-1">
                              <Calendar className="h-4 w-4 text-muted-foreground" />
                              <span className="text-sm">
                                {new Date(
                                  panorama.createdAt
                                ).toLocaleDateString()}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Switch
                              checked={panorama.isActive}
                              onCheckedChange={() =>
                                toggleActive(panorama.id, panorama.isActive)
                              }
                            />
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end space-x-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                  window.open(
                                    `/panorama/${panorama.id}`,
                                    "_blank"
                                  )
                                }
                                title={t(
                                  "admin.panoramasPage.actions.viewPanorama",
                                  "View Panorama"
                                )}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                  window.open(`/embed/${panorama.id}`, "_blank")
                                }
                                title={t(
                                  "admin.panoramasPage.actions.previewEmbed",
                                  "Preview Embed"
                                )}
                              >
                                <ExternalLink className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleShowIframe(panorama)}
                                title={t(
                                  "admin.panoramasPage.actions.getEmbedCode",
                                  "Get Embed Code"
                                )}
                              >
                                <Code className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                  window.open(
                                    `/admin/hotspots/visual-editor?panoramaId=${panorama.id}`,
                                    "_blank"
                                  )
                                }
                                title={t(
                                  "admin.panoramasPage.actions.visualEditor",
                                  "Visual Hotspot Editor"
                                )}
                              >
                                <Target className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEdit(panorama)}
                                title={t(
                                  "admin.panoramasPage.actions.editPanorama",
                                  "Edit Panorama"
                                )}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDelete(panorama.id)}
                                disabled={panorama._count.hotspots > 0}
                                title={t(
                                  "admin.panoramasPage.actions.deletePanorama",
                                  "Delete Panorama"
                                )}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* Mobile Card View */}
                <div className="md:hidden space-y-4">
                  {panoramas.map((panorama) => (
                    <Card
                      key={panorama.id}
                      className="border border-border/50 shadow-sm"
                    >
                      <CardContent className="p-4">
                        {/* Header with thumbnail and title */}
                        <div className="flex items-start space-x-3 mb-4">
                          <div className="w-20 h-12 bg-muted rounded-lg overflow-hidden flex items-center justify-center flex-shrink-0">
                            {panorama.thumbnailPath ? (
                              <img
                                src={`${
                                  process.env.NEXT_PUBLIC_API_URL ||
                                  "http://localhost:5000"
                                }${panorama.thumbnailPath}`}
                                alt={panorama.title}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  e.currentTarget.style.display = "none";
                                  e.currentTarget.nextElementSibling?.classList.remove(
                                    "hidden"
                                  );
                                }}
                              />
                            ) : null}
                            <ImageIcon
                              className={`h-6 w-6 text-muted-foreground ${
                                panorama.thumbnailPath ? "hidden" : ""
                              }`}
                            />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="font-semibold text-base leading-tight mb-1">
                              {panorama.title}
                            </h3>
                            <p className="text-sm text-muted-foreground line-clamp-2">
                              {panorama.description ||
                                t(
                                  "admin.panoramasPage.table.noDescription",
                                  "No description"
                                )}
                            </p>
                          </div>
                          <div className="flex items-center space-x-2 flex-shrink-0">
                            <Badge
                              variant={
                                panorama.isActive ? "default" : "secondary"
                              }
                              className="text-xs"
                            >
                              {panorama.isActive
                                ? t(
                                    "admin.panoramasPage.table.statusActive",
                                    "Active"
                                  )
                                : t(
                                    "admin.panoramasPage.table.statusInactive",
                                    "Inactive"
                                  )}
                            </Badge>
                          </div>
                        </div>

                        {/* Stats Row */}
                        <div className="flex items-center justify-between mb-4 text-sm">
                          <div className="flex items-center space-x-4">
                            <div className="flex items-center space-x-1">
                              <MapPin className="h-4 w-4 text-muted-foreground" />
                              <span className="text-muted-foreground">
                                {panorama._count.hotspots} hotspots
                              </span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Calendar className="h-4 w-4 text-muted-foreground" />
                              <span className="text-muted-foreground">
                                {new Date(
                                  panorama.createdAt
                                ).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-muted-foreground">
                              Active
                            </span>
                            <Switch
                              checked={panorama.isActive}
                              onCheckedChange={() =>
                                toggleActive(panorama.id, panorama.isActive)
                              }
                            />
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="grid grid-cols-2 gap-2">
                          {/* Primary Actions Row */}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              window.open(`/panorama/${panorama.id}`, "_blank")
                            }
                            className="flex items-center justify-center space-x-2"
                          >
                            <Eye className="h-4 w-4" />
                            <span>View</span>
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              window.open(
                                `/admin/hotspots/visual-editor?panoramaId=${panorama.id}`,
                                "_blank"
                              )
                            }
                            className="flex items-center justify-center space-x-2"
                          >
                            <Target className="h-4 w-4" />
                            <span>Hotspots</span>
                          </Button>

                          {/* Secondary Actions Row */}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(panorama)}
                            className="flex items-center justify-center space-x-2"
                          >
                            <Edit className="h-4 w-4" />
                            <span>Edit</span>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleShowIframe(panorama)}
                            className="flex items-center justify-center space-x-2"
                          >
                            <Code className="h-4 w-4" />
                            <span>Embed</span>
                          </Button>
                        </div>

                        {/* Additional Actions - Collapsible */}
                        <div className="mt-3 pt-3 border-t border-border/50">
                          <div className="flex items-center justify-between">
                            <div className="flex space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                  window.open(`/embed/${panorama.id}`, "_blank")
                                }
                                title={t(
                                  "admin.panoramasPage.actions.previewEmbed",
                                  "Preview Embed"
                                )}
                              >
                                <ExternalLink className="h-4 w-4" />
                              </Button>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(panorama.id)}
                              disabled={panorama._count.hotspots > 0}
                              className="text-destructive hover:text-destructive"
                              title={
                                panorama._count.hotspots > 0
                                  ? "Cannot delete panorama with hotspots"
                                  : t(
                                      "admin.panoramasPage.actions.deletePanorama",
                                      "Delete Panorama"
                                    )
                              }
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Iframe Code Dialog */}
        <Dialog open={showIframeDialog} onOpenChange={setShowIframeDialog}>
          <DialogContent className="max-w-[calc(100vw-24px)] sm:max-w-[600px] max-h-[90vh] overflow-y-auto rounded-lg">
            <DialogHeader>
              <DialogTitle>
                {t(
                  "admin.panoramasPage.embedDialog.title",
                  "Embed Code for {title}",
                  {
                    title: selectedPanoramaForIframe?.title,
                  }
                )}
              </DialogTitle>
              <DialogDescription>
                {t(
                  "admin.panoramasPage.embedDialog.description",
                  "Copy the iframe code below to embed this panorama on your website or WordPress."
                )}
              </DialogDescription>
            </DialogHeader>

            {selectedPanoramaForIframe && (
              <div className="space-y-4">
                {/* Preview */}
                <div className="space-y-2">
                  <Label>
                    {t("admin.panoramasPage.embedDialog.preview", "Preview")}
                  </Label>
                  <div className="border rounded-lg p-4 bg-muted">
                    <iframe
                      src={`/embed/${selectedPanoramaForIframe.id}`}
                      width="100%"
                      height="300"
                      style={{ border: 0 }}
                      allowFullScreen
                      title={`${selectedPanoramaForIframe.title} - Preview`}
                      className="rounded"
                    />
                  </div>
                </div>

                {/* Embed URLs */}
                <div className="space-y-2">
                  <Label>
                    {t(
                      "admin.panoramasPage.embedDialog.directUrls",
                      "Direct URLs"
                    )}
                  </Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Input
                        readOnly
                        value={`${
                          typeof window !== "undefined"
                            ? window.location.origin
                            : ""
                        }/panorama/${selectedPanoramaForIframe.id}`}
                        className="flex-1"
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          copyToClipboard(
                            `${window.location.origin}/panorama/${selectedPanoramaForIframe.id}`
                          )
                        }
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Input
                        readOnly
                        value={`${
                          typeof window !== "undefined"
                            ? window.location.origin
                            : ""
                        }/embed/${selectedPanoramaForIframe.id}`}
                        className="flex-1"
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          copyToClipboard(
                            `${window.location.origin}/embed/${selectedPanoramaForIframe.id}`
                          )
                        }
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Iframe Code */}
                <div className="space-y-2">
                  <Label>
                    {t(
                      "admin.panoramasPage.embedDialog.iframeCode",
                      "Iframe Embed Code"
                    )}
                  </Label>
                  <div className="relative">
                    <Textarea
                      readOnly
                      value={generateIframeCode(selectedPanoramaForIframe)}
                      className="font-mono text-sm min-h-[120px]"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      className="absolute top-2 right-2"
                      onClick={() =>
                        copyToClipboard(
                          generateIframeCode(selectedPanoramaForIframe)
                        )
                      }
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* WordPress Instructions */}
                <div className="space-y-2">
                  <Label>
                    {t(
                      "admin.panoramasPage.embedDialog.wordpressInstructions",
                      "WordPress Instructions"
                    )}
                  </Label>
                  <div className="text-sm text-muted-foreground bg-blue-50 p-3 rounded border border-blue-200">
                    <p className="font-medium text-blue-800 mb-2">
                      {t(
                        "admin.panoramasPage.embedDialog.wordpressTitle",
                        "📝 How to embed in WordPress:"
                      )}
                    </p>
                    <ol className="list-decimal list-inside space-y-1 text-blue-700">
                      <li>
                        {t(
                          "admin.panoramasPage.embedDialog.wordpressSteps.step1",
                          "Copy the iframe code above"
                        )}
                      </li>
                      <li>
                        {t(
                          "admin.panoramasPage.embedDialog.wordpressSteps.step2",
                          'In WordPress editor, switch to "Text" or "HTML" mode'
                        )}
                      </li>
                      <li>
                        {t(
                          "admin.panoramasPage.embedDialog.wordpressSteps.step3",
                          "Paste the iframe code where you want the panorama"
                        )}
                      </li>
                      <li>
                        {t(
                          "admin.panoramasPage.embedDialog.wordpressSteps.step4",
                          'Switch back to "Visual" mode and publish'
                        )}
                      </li>
                    </ol>
                  </div>
                </div>
              </div>
            )}

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowIframeDialog(false)}
              >
                {t("admin.panoramasPage.embedDialog.close", "Close")}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AdminLayout>
  );
};

export default PanoramasPage;
