// frontend\src\pages\admin\hotspots.tsx

import React, { useState, useEffect } from "react";
import AdminLayout from "@/components/AdminLayout";
import { EnhancedHotspotContentForm } from "@/components/EnhancedHotspotContentForm";
import MultiProductManager from "@/components/MultiProductManager";
import axios from "axios";
import { toast } from "react-hot-toast";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import {
  Plus,
  Edit,
  Trash2,
  Image as ImageIcon,
  Languages,
  Target,
  Package,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useAppTranslation } from "@/hooks/useAppTranslation";

interface HotspotContent {
  id?: string;
  title: string;
  subtitle?: string;
  description: string;
  imageUrl?: string;
  linkUrl?: string;
  linkText?: string;
  languageCode: string;
  order?: number;
  hotspotId?: string;
  titleStyle?: any;
  subtitleStyle?: any;
  descriptionStyle?: any;
  linkStyle?: any;
  imageStyle?: any;
}

interface Hotspot {
  id: string;
  xPosition: number;
  yPosition: number;
  iconType: string;
  iconColor: string;
  panoramaId: string;
  panorama: {
    title: string;
  };
  createdAt: string;
  content?: HotspotContent[];
  _count: {
    content: number;
  };
}

interface Panorama {
  id: string;
  title: string;
}

interface Language {
  code: string;
  name: string;
  isActive: boolean;
}

// Schemas will be created inside the component to access translations

type HotspotFormData = {
  xPosition: number;
  yPosition: number;
  iconType: string;
  iconColor: string;
};

const HotspotsExtendedPage: React.FC = () => {
  const { t } = useAppTranslation();
  const [hotspots, setHotspots] = useState<Hotspot[]>([]);
  const [allHotspots, setAllHotspots] = useState<Hotspot[]>([]); // For counts in dropdown
  const [panoramas, setPanoramas] = useState<Panorama[]>([]);
  const [languages, setLanguages] = useState<Language[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [showContentForm, setShowContentForm] = useState(false);
  const [editingHotspot, setEditingHotspot] = useState<Hotspot | null>(null);
  const [editingContent, setEditingContent] = useState<HotspotContent | null>(
    null
  );
  const [selectedHotspotForContent, setSelectedHotspotForContent] =
    useState<Hotspot | null>(null);
  const [selectedPanoramaFilter, setSelectedPanoramaFilter] =
    useState<string>("all");

  // Create schemas with translations
  const hotspotSchema = yup.object({
    xPosition: yup
      .number()
      .min(0)
      .max(100)
      .required(
        t(
          "admin.hotspotsPage.hotspotForm.validation.xPositionRequired",
          "X position is required"
        )
      ),
    yPosition: yup
      .number()
      .min(0)
      .max(100)
      .required(
        t(
          "admin.hotspotsPage.hotspotForm.validation.yPositionRequired",
          "Y position is required"
        )
      ),
    iconType: yup
      .string()
      .required(
        t(
          "admin.hotspotsPage.hotspotForm.validation.iconTypeRequired",
          "Icon type is required"
        )
      ),
    iconColor: yup
      .string()
      .required(
        t(
          "admin.hotspotsPage.hotspotForm.validation.iconColorRequired",
          "Icon color is required"
        )
      ),
  });

  // Hotspot form
  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<HotspotFormData>({
    resolver: yupResolver(hotspotSchema),
  });

  const [isSubmittingContent, setIsSubmittingContent] = useState(false);

  // Multi-product manager state
  const [showMultiProductManager, setShowMultiProductManager] = useState(false);
  const [selectedHotspotForProducts, setSelectedHotspotForProducts] =
    useState<Hotspot | null>(null);

  useEffect(() => {
    fetchData();
  }, []);

  // Refetch hotspots when panorama filter changes
  useEffect(() => {
    if (panoramas.length > 0) {
      fetchData();
    }
  }, [selectedPanoramaFilter]);

  // Update selected hotspot when hotspots data changes
  useEffect(() => {
    if (selectedHotspotForProducts && hotspots.length > 0) {
      const updatedHotspot = hotspots.find(
        (h) => h.id === selectedHotspotForProducts.id
      );
      if (updatedHotspot) {
        setSelectedHotspotForProducts(updatedHotspot);
      }
    }
  }, [hotspots]);

  const fetchData = async () => {
    try {
      const token = localStorage.getItem("token");
      const headers = { Authorization: `Bearer ${token}` };

      // Build hotspots URL with panorama filter
      const hotspotsUrl =
        selectedPanoramaFilter === "all"
          ? `${process.env.NEXT_PUBLIC_API_URL}/api/hotspots`
          : `${process.env.NEXT_PUBLIC_API_URL}/api/hotspots?panoramaId=${selectedPanoramaFilter}`;

      const requests = [
        axios.get(hotspotsUrl, { headers }),
        axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/panoramas`, {
          headers,
        }),
        axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/languages`, {
          headers,
        }),
      ];

      // Also fetch all hotspots for counts if we're filtering
      if (selectedPanoramaFilter !== "all") {
        requests.push(
          axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/hotspots`, {
            headers,
          })
        );
      }

      const responses = await Promise.all(requests);
      const [hotspotsRes, panoramasRes, languagesRes, allHotspotsRes] =
        responses;

      setHotspots(hotspotsRes.data.data.hotspots || []);
      setPanoramas(panoramasRes.data.data.panoramas || []);
      setLanguages(languagesRes.data.data.languages || []);

      // Set all hotspots for counts
      if (allHotspotsRes) {
        setAllHotspots(allHotspotsRes.data.data.hotspots || []);
      } else {
        setAllHotspots(hotspotsRes.data.data.hotspots || []);
      }
    } catch (error) {
      toast.error(
        t("admin.hotspotsPage.messages.fetchFailed", "Failed to fetch data")
      );
      setHotspots([]);
      setPanoramas([]);
      setLanguages([]);
    } finally {
      setLoading(false);
    }
  };

  const onSubmitHotspot = async (data: HotspotFormData) => {
    try {
      const token = localStorage.getItem("token");
      const headers = { Authorization: `Bearer ${token}` };

      // Use the selected panorama from the filter
      const submitData = {
        ...data,
        panoramaId: selectedPanoramaFilter,
      };

      if (editingHotspot) {
        await axios.put(
          `${process.env.NEXT_PUBLIC_API_URL}/api/hotspots/${editingHotspot.id}`,
          submitData,
          { headers }
        );
        toast.success(
          t(
            "admin.hotspotsPage.messages.hotspotUpdateSuccess",
            "Hotspot updated successfully"
          )
        );
      } else {
        await axios.post(
          `${process.env.NEXT_PUBLIC_API_URL}/api/hotspots`,
          submitData,
          { headers }
        );
        toast.success(
          t(
            "admin.hotspotsPage.messages.hotspotCreateSuccess",
            "Hotspot created successfully"
          )
        );
      }

      fetchData();
      handleCloseForm();
    } catch (error: any) {
      const message =
        error.response?.data?.error?.message || "Operation failed";
      toast.error(message);
    }
  };

  const onSubmitContent = async (data: any) => {
    setIsSubmittingContent(true);
    try {
      const token = localStorage.getItem("token");
      const headers = { Authorization: `Bearer ${token}` };

      if (editingContent) {
        await axios.put(
          `${process.env.NEXT_PUBLIC_API_URL}/api/hotspots/content/${editingContent.id}`,
          data,
          { headers }
        );
        toast.success(
          t(
            "admin.hotspotsPage.messages.contentUpdateSuccess",
            "Content updated successfully"
          )
        );
      } else {
        const payload = { ...data, hotspotId: selectedHotspotForContent?.id };
        await axios.post(
          `${process.env.NEXT_PUBLIC_API_URL}/api/hotspots/content`,
          payload,
          { headers }
        );
        toast.success(
          t(
            "admin.hotspotsPage.messages.contentCreateSuccess",
            "Content created successfully"
          )
        );
      }

      fetchData();
      handleCloseContentForm();
    } catch (error: any) {
      const message =
        error.response?.data?.error?.message || "Operation failed";
      toast.error(message);
    } finally {
      setIsSubmittingContent(false);
    }
  };

  const handleEditHotspot = (hotspot: Hotspot) => {
    setEditingHotspot(hotspot);
    setValue("xPosition", hotspot.xPosition);
    setValue("yPosition", hotspot.yPosition);
    setValue("iconType", hotspot.iconType);
    setValue("iconColor", hotspot.iconColor);
    // Set the panorama filter to match the hotspot's panorama
    setSelectedPanoramaFilter(hotspot.panoramaId);
    setShowForm(true);
  };

  const handleEditContent = (content: HotspotContent, hotspot: Hotspot) => {
    setEditingContent(content);
    setSelectedHotspotForContent(hotspot);
    setShowContentForm(true);
  };

  const handleAddContent = (hotspot: Hotspot) => {
    setSelectedHotspotForContent(hotspot);
    setEditingContent(null);
    setShowContentForm(true);
  };

  const handleDeleteHotspot = async (id: string) => {
    if (
      !confirm(
        t(
          "admin.hotspotsPage.messages.hotspotDeleteConfirm",
          "Are you sure you want to delete this hotspot?"
        )
      )
    )
      return;

    try {
      const token = localStorage.getItem("token");
      await axios.delete(
        `${process.env.NEXT_PUBLIC_API_URL}/api/hotspots/${id}`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      toast.success(
        t(
          "admin.hotspotsPage.messages.hotspotDeleteSuccess",
          "Hotspot deleted successfully"
        )
      );
      fetchData();
    } catch (error: any) {
      const message = error.response?.data?.error?.message || "Delete failed";
      toast.error(message);
    }
  };

  const handleDeleteContent = async (contentId: string) => {
    if (
      !confirm(
        t(
          "admin.hotspotsPage.messages.contentDeleteConfirm",
          "Are you sure you want to delete this content?"
        )
      )
    )
      return;

    try {
      const token = localStorage.getItem("token");
      await axios.delete(
        `${process.env.NEXT_PUBLIC_API_URL}/api/hotspots/content/${contentId}`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      toast.success(
        t(
          "admin.hotspotsPage.messages.contentDeleteSuccess",
          "Content deleted successfully"
        )
      );
      fetchData();
    } catch (error: any) {
      const message = error.response?.data?.error?.message || "Delete failed";
      toast.error(message);
    }
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setEditingHotspot(null);
    reset();
  };

  const handleCloseContentForm = () => {
    setShowContentForm(false);
    setEditingContent(null);
    setSelectedHotspotForContent(null);
  };

  // Multi-product manager handlers
  const handleManageProducts = (hotspot: Hotspot) => {
    setSelectedHotspotForProducts(hotspot);
    setShowMultiProductManager(true);
  };

  const handleProductAdd = async (product: HotspotContent) => {
    try {
      const token = localStorage.getItem("token");
      const headers = { Authorization: `Bearer ${token}` };

      const payload = {
        ...product,
        hotspotId: selectedHotspotForProducts?.id,
        order: product.order || 0,
      };

      await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL}/api/hotspots/content`,
        payload,
        { headers }
      );

      toast.success(
        t("admin.multiProduct.productAdded", "Product added successfully")
      );

      // Refresh data to get updated products list
      await fetchData();
    } catch (error) {
      console.error("Error adding product:", error);
      toast.error(t("admin.multiProduct.addError", "Failed to add product"));
    }
  };

  const handleProductUpdate = async (product: HotspotContent) => {
    try {
      const token = localStorage.getItem("token");
      const headers = { Authorization: `Bearer ${token}` };

      await axios.put(
        `${process.env.NEXT_PUBLIC_API_URL}/api/hotspots/content/${product.id}`,
        product,
        { headers }
      );

      toast.success(
        t("admin.multiProduct.productUpdated", "Product updated successfully")
      );

      // Refresh data to get updated products list
      await fetchData();

      // Update the selected hotspot with fresh data
      const updatedHotspot = hotspots.find(
        (h) => h.id === selectedHotspotForProducts?.id
      );
      if (updatedHotspot) {
        setSelectedHotspotForProducts(updatedHotspot);
      }
    } catch (error) {
      console.error("Error updating product:", error);
      toast.error(
        t("admin.multiProduct.updateError", "Failed to update product")
      );
    }
  };

  const handleProductDelete = async (productId: string) => {
    try {
      const token = localStorage.getItem("token");
      const headers = { Authorization: `Bearer ${token}` };

      await axios.delete(
        `${process.env.NEXT_PUBLIC_API_URL}/api/hotspots/content/${productId}`,
        { headers }
      );

      toast.success(
        t("admin.multiProduct.productDeleted", "Product deleted successfully")
      );

      // Refresh data to get updated products list
      await fetchData();
    } catch (error) {
      console.error("Error deleting product:", error);
      toast.error(
        t("admin.multiProduct.deleteError", "Failed to delete product")
      );
    }
  };

  const handleProductReorder = async (productIds: string[]) => {
    try {
      const token = localStorage.getItem("token");
      const headers = { Authorization: `Bearer ${token}` };

      await axios.put(
        `${process.env.NEXT_PUBLIC_API_URL}/api/hotspots/${selectedHotspotForProducts?.id}/content/reorder`,
        { contentIds: productIds },
        { headers }
      );

      toast.success(
        t(
          "admin.multiProduct.orderUpdated",
          "Product order updated successfully"
        )
      );

      // Refresh data to get updated products list
      await fetchData();
    } catch (error) {
      console.error("Error reordering products:", error);
      toast.error(
        t("admin.multiProduct.reorderError", "Failed to reorder products")
      );
    }
  };

  // Stats are now based on the filtered hotspots from backend
  const stats = {
    total: hotspots.length,
    totalContent: hotspots.reduce((sum, h) => sum + h._count.content, 0),
    panoramasWithHotspots: new Set(hotspots.map((h) => h.panoramaId)).size,
  };

  const iconTypes = [
    { value: "info", label: t("admin.hotspotsPage.iconTypes.info", "Info") },
    {
      value: "warning",
      label: t("admin.hotspotsPage.iconTypes.warning", "Warning"),
    },
    {
      value: "success",
      label: t("admin.hotspotsPage.iconTypes.success", "Success"),
    },
    { value: "error", label: t("admin.hotspotsPage.iconTypes.error", "Error") },
  ];

  const iconColors = [
    {
      value: "#ffffff",
      label: t("admin.hotspotsPage.iconColors.white", "White"),
      color: "bg-white border",
    },
    {
      value: "#007bff",
      label: t("admin.hotspotsPage.iconColors.blue", "Blue"),
      color: "bg-blue-500",
    },
    {
      value: "#28a745",
      label: t("admin.hotspotsPage.iconColors.green", "Green"),
      color: "bg-green-500",
    },
    {
      value: "#dc3545",
      label: t("admin.hotspotsPage.iconColors.red", "Red"),
      color: "bg-red-500",
    },
    {
      value: "#ffc107",
      label: t("admin.hotspotsPage.iconColors.yellow", "Yellow"),
      color: "bg-yellow-500",
    },
    {
      value: "#6f42c1",
      label: t("admin.hotspotsPage.iconColors.purple", "Purple"),
      color: "bg-purple-500",
    },
  ];

  if (loading) {
    return (
      <AdminLayout title={t("admin.hotspotsPage.title", "Hotspots & Content")}>
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="text-sm text-muted-foreground">
              {t("admin.hotspotsPage.loading", "Loading hotspots...")}
            </p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title={t("admin.hotspotsPage.title", "Hotspots & Content")}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col space-y-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div>
              <h2 className="text-2xl sm:text-3xl font-bold tracking-tight">
                {t("admin.hotspotsPage.title", "Hotspots & Content")}
              </h2>
              <p className="text-muted-foreground text-sm sm:text-base">
                {t(
                  "admin.hotspotsPage.subtitle",
                  "Manage interactive hotspots and their content for panoramas"
                )}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              {/* Buttons moved to Filter section below */}
            </div>
          </div>
        </div>

        {/* Panorama Filter */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">
              {t("admin.hotspotsPage.filter.title", "Filter by Panorama")}
            </CardTitle>
            <CardDescription>
              {t(
                "admin.hotspotsPage.filter.description",
                "Select a panorama to view its hotspots and content"
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
              <Label
                htmlFor="panorama-filter"
                className="text-sm font-medium sm:flex-shrink-0"
              >
                {t("admin.hotspotsPage.filter.label", "Panorama:")}
              </Label>
              <Select
                value={selectedPanoramaFilter}
                onValueChange={setSelectedPanoramaFilter}
              >
                <SelectTrigger className="w-full sm:w-[300px]">
                  <SelectValue
                    placeholder={t(
                      "admin.hotspotsPage.filter.placeholder",
                      "Select a panorama"
                    )}
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    {t(
                      "admin.hotspotsPage.filter.allPanoramas",
                      "All Panoramas"
                    )}{" "}
                    ({allHotspots.length})
                  </SelectItem>
                  {panoramas.map((panorama) => {
                    const hotspotCount = allHotspots.filter(
                      (h) => h.panoramaId === panorama.id
                    ).length;
                    return (
                      <SelectItem key={panorama.id} value={panorama.id}>
                        {panorama.title} ({hotspotCount})
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
              {selectedPanoramaFilter !== "all" && (
                <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full sm:w-auto">
                  <Dialog open={showForm} onOpenChange={setShowForm}>
                    <DialogTrigger asChild>
                      <Button size="sm" className="w-full sm:w-auto">
                        <Plus className="mr-2 h-4 w-4" />
                        {t("admin.hotspotsPage.addHotspot", "Add Hotspot")}
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-[calc(100vw-24px)] sm:max-w-[425px] max-h-[90vh] overflow-y-auto rounded-lg">
                      <form onSubmit={handleSubmit(onSubmitHotspot)}>
                        <DialogHeader>
                          <DialogTitle>
                            {editingHotspot
                              ? t(
                                  "admin.hotspotsPage.hotspotForm.editTitle",
                                  "Edit Hotspot"
                                )
                              : t(
                                  "admin.hotspotsPage.hotspotForm.addTitle",
                                  "Add Hotspot"
                                )}
                          </DialogTitle>
                          <DialogDescription>
                            {editingHotspot
                              ? t(
                                  "admin.hotspotsPage.hotspotForm.editDescription",
                                  "Update the hotspot details below."
                                )
                              : t(
                                  "admin.hotspotsPage.hotspotForm.addDescription",
                                  "Add a new interactive hotspot to a panorama."
                                )}
                          </DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                          {/* Show selected panorama info */}
                          <div className="space-y-2">
                            <Label>
                              {t(
                                "admin.hotspotsPage.hotspotForm.panoramaLabel",
                                "Panorama"
                              )}
                            </Label>
                            <div className="p-3 bg-muted rounded-md">
                              <p className="font-medium">
                                {panoramas.find(
                                  (p) => p.id === selectedPanoramaFilter
                                )?.title || "Unknown Panorama"}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {t(
                                  "admin.hotspotsPage.hotspotForm.panoramaNote",
                                  "Hotspot will be added to this panorama"
                                )}
                              </p>
                            </div>
                          </div>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor="xPosition">
                                {t(
                                  "admin.hotspotsPage.hotspotForm.xPositionLabel",
                                  "X Position (%)"
                                )}
                              </Label>
                              <Input
                                id="xPosition"
                                type="number"
                                min="0"
                                max="100"
                                step="0.1"
                                placeholder={t(
                                  "admin.hotspotsPage.hotspotForm.xPositionPlaceholder",
                                  "25.5"
                                )}
                                {...register("xPosition", {
                                  valueAsNumber: true,
                                })}
                              />
                              {errors.xPosition && (
                                <p className="text-sm text-destructive">
                                  {errors.xPosition.message}
                                </p>
                              )}
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="yPosition">
                                {t(
                                  "admin.hotspotsPage.hotspotForm.yPositionLabel",
                                  "Y Position (%)"
                                )}
                              </Label>
                              <Input
                                id="yPosition"
                                type="number"
                                min="0"
                                max="100"
                                step="0.1"
                                placeholder={t(
                                  "admin.hotspotsPage.hotspotForm.yPositionPlaceholder",
                                  "45.2"
                                )}
                                {...register("yPosition", {
                                  valueAsNumber: true,
                                })}
                              />
                              {errors.yPosition && (
                                <p className="text-sm text-destructive">
                                  {errors.yPosition.message}
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="iconType">
                              {t(
                                "admin.hotspotsPage.hotspotForm.iconTypeLabel",
                                "Icon Type"
                              )}
                            </Label>
                            <Select
                              value={watch("iconType")}
                              onValueChange={(value) =>
                                setValue("iconType", value)
                              }
                            >
                              <SelectTrigger>
                                <SelectValue
                                  placeholder={t(
                                    "admin.hotspotsPage.hotspotForm.iconTypePlaceholder",
                                    "Select icon type"
                                  )}
                                />
                              </SelectTrigger>
                              <SelectContent>
                                {iconTypes.map((type) => (
                                  <SelectItem
                                    key={type.value}
                                    value={type.value}
                                  >
                                    {type.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            {errors.iconType && (
                              <p className="text-sm text-destructive">
                                {errors.iconType.message}
                              </p>
                            )}
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="iconColor">
                              {t(
                                "admin.hotspotsPage.hotspotForm.iconColorLabel",
                                "Icon Color"
                              )}
                            </Label>
                            <Select
                              value={watch("iconColor")}
                              onValueChange={(value) =>
                                setValue("iconColor", value)
                              }
                            >
                              <SelectTrigger>
                                <SelectValue
                                  placeholder={t(
                                    "admin.hotspotsPage.hotspotForm.iconColorPlaceholder",
                                    "Select icon color"
                                  )}
                                />
                              </SelectTrigger>
                              <SelectContent>
                                {iconColors.map((color) => (
                                  <SelectItem
                                    key={color.value}
                                    value={color.value}
                                  >
                                    <div className="flex items-center space-x-2">
                                      <div
                                        className={`w-4 h-4 rounded-full ${color.color}`}
                                      />
                                      <span>{color.label}</span>
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            {errors.iconColor && (
                              <p className="text-sm text-destructive">
                                {errors.iconColor.message}
                              </p>
                            )}
                          </div>
                        </div>
                        <DialogFooter className="flex flex-col sm:flex-row gap-3 sm:gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={handleCloseForm}
                            className="w-full sm:w-auto"
                          >
                            {t(
                              "admin.hotspotsPage.hotspotForm.cancel",
                              "Cancel"
                            )}
                          </Button>
                          <Button
                            type="submit"
                            disabled={isSubmitting}
                            className="w-full sm:w-auto"
                          >
                            {isSubmitting
                              ? t(
                                  "admin.hotspotsPage.hotspotForm.saving",
                                  "Saving..."
                                )
                              : editingHotspot
                              ? t(
                                  "admin.hotspotsPage.hotspotForm.update",
                                  "Update"
                                )
                              : t(
                                  "admin.hotspotsPage.hotspotForm.create",
                                  "Create"
                                )}
                          </Button>
                        </DialogFooter>
                      </form>
                    </DialogContent>
                  </Dialog>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full sm:w-auto"
                    onClick={() => {
                      window.open(
                        `/admin/hotspots/visual-editor?panoramaId=${selectedPanoramaFilter}`,
                        "_blank"
                      );
                    }}
                  >
                    <Target className="mr-2 h-4 w-4" />
                    {t("admin.hotspotsPage.visualEditor", "Visual Editor")}
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Stats Cards */}
        <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-3">
          <Card className="border-border/50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t("admin.hotspotsPage.stats.totalHotspots", "Total Hotspots")}
              </CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
              <p className="text-xs text-muted-foreground">
                {t(
                  "admin.hotspotsPage.stats.interactivePoints",
                  "Interactive points"
                )}
              </p>
            </CardContent>
          </Card>
          <Card className="border-border/50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t(
                  "admin.hotspotsPage.stats.panoramasWithHotspots",
                  "Panoramas with Hotspots"
                )}
              </CardTitle>
              <ImageIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.panoramasWithHotspots}
              </div>
              <p className="text-xs text-muted-foreground">
                {t(
                  "admin.hotspotsPage.stats.outOfTotal",
                  "Out of {count} total",
                  {
                    count: panoramas.length,
                  }
                )}
              </p>
            </CardContent>
          </Card>
          <Card className="border-border/50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t("admin.hotspotsPage.stats.totalContent", "Total Content")}
              </CardTitle>
              <Languages className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalContent}</div>
              <p className="text-xs text-muted-foreground">
                {t("admin.hotspotsPage.stats.contentItems", "Content items")}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Hotspots Management */}
        <Card>
          <CardHeader>
            <CardTitle>
              {t(
                "admin.hotspotsPage.management.title",
                "Hotspot & Content Management"
              )}
            </CardTitle>
            <CardDescription>
              {t(
                "admin.hotspotsPage.management.subtitle",
                "Configure hotspots and manage their content across languages"
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {hotspots.length === 0 ? (
              <div className="text-center py-8">
                <Target className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="mt-2 text-sm font-semibold">
                  {t("admin.hotspotsPage.management.noHotspots", "No hotspots")}
                </h3>
                <p className="mt-1 text-sm text-muted-foreground">
                  {panoramas.length === 0
                    ? t(
                        "admin.hotspotsPage.management.noPanoramasMessage",
                        "Add a panorama first, then create hotspots for it."
                      )
                    : t(
                        "admin.hotspotsPage.management.noHotspotsMessage",
                        "Get started by adding your first interactive hotspot."
                      )}
                </p>
                {panoramas.length > 0 && (
                  <div className="mt-6">
                    <Button onClick={() => setShowForm(true)}>
                      <Plus className="mr-2 h-4 w-4" />
                      {t("admin.hotspotsPage.addHotspot", "Add Hotspot")}
                    </Button>
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-6">
                {hotspots.map((hotspot) => (
                  <Card
                    key={hotspot.id}
                    className="border-l-4 border-l-primary"
                  >
                    <CardHeader>
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                        <div className="flex items-center space-x-3">
                          <div
                            className="w-6 h-6 rounded-full border-2 border-gray-300 flex-shrink-0"
                            style={{ backgroundColor: hotspot.iconColor }}
                          />
                          <div className="min-w-0 flex-1">
                            <h4 className="font-semibold">
                              {hotspot.panorama.title}
                            </h4>
                            <p className="text-sm text-muted-foreground">
                              {t(
                                "admin.hotspotsPage.management.position",
                                "Position: {x}%, {y}%",
                                {
                                  x: hotspot.xPosition.toFixed(1),
                                  y: hotspot.yPosition.toFixed(1),
                                }
                              )}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center justify-between sm:justify-end space-x-2">
                          <Badge variant="outline">{hotspot.iconType}</Badge>
                          <div className="flex items-center space-x-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditHotspot(hotspot)}
                              title="Edit Hotspot"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteHotspot(hotspot.id)}
                              disabled={hotspot._count.content > 0}
                              title={
                                hotspot._count.content > 0
                                  ? "Cannot delete hotspot with content"
                                  : "Delete Hotspot"
                              }
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                          <h5 className="font-medium">
                            {t(
                              "admin.hotspotsPage.management.contentCount",
                              "Content ({count} items)",
                              {
                                count: hotspot._count.content,
                              }
                            )}
                          </h5>
                          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                            <Button
                              variant="default"
                              size="sm"
                              onClick={() => handleManageProducts(hotspot)}
                              className="w-full sm:w-auto"
                            >
                              <Package className="mr-2 h-4 w-4" />
                              {t(
                                "admin.hotspotsPage.management.manageProducts",
                                "Manage Products"
                              )}
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleAddContent(hotspot)}
                              className="w-full sm:w-auto"
                            >
                              <Plus className="mr-2 h-4 w-4" />
                              {t(
                                "admin.hotspotsPage.management.addContent",
                                "Add Content"
                              )}
                            </Button>
                          </div>
                        </div>

                        {hotspot.content && hotspot.content.length > 0 ? (
                          <div className="grid gap-3">
                            {hotspot.content.map((content) => (
                              <div
                                key={content.id}
                                className="border rounded-lg p-3 bg-muted/50"
                              >
                                <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between space-y-3 sm:space-y-0">
                                  <div className="flex-1 min-w-0">
                                    <div className="flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-2 mb-2">
                                      <h6 className="font-medium">
                                        {content.title}
                                      </h6>
                                      <Badge
                                        variant="secondary"
                                        className="text-xs w-fit"
                                      >
                                        {content.languageCode.toUpperCase()}
                                      </Badge>
                                    </div>
                                    {content.subtitle && (
                                      <p className="text-sm text-muted-foreground mb-1">
                                        {content.subtitle}
                                      </p>
                                    )}
                                    <p className="text-sm text-muted-foreground line-clamp-2">
                                      {content.description}
                                    </p>
                                    {content.linkText && (
                                      <p className="text-xs text-primary mt-1">
                                        {t(
                                          "admin.hotspotsPage.management.linkLabel",
                                          "Link: {text}",
                                          {
                                            text: content.linkText,
                                          }
                                        )}
                                      </p>
                                    )}
                                  </div>
                                  <div className="flex items-center justify-end space-x-1 sm:ml-2 flex-shrink-0">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() =>
                                        handleEditContent(content, hotspot)
                                      }
                                      title="Edit Content"
                                    >
                                      <Edit className="h-3 w-3" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() =>
                                        handleDeleteContent(content.id!)
                                      }
                                      title="Delete Content"
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-4 text-sm text-muted-foreground">
                            {t(
                              "admin.hotspotsPage.management.noContentMessage",
                              'No content added yet. Click "Add Content" to get started.'
                            )}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Enhanced Content Form */}
        <EnhancedHotspotContentForm
          isOpen={showContentForm}
          onClose={handleCloseContentForm}
          onSubmit={onSubmitContent}
          initialData={editingContent || undefined}
          languages={languages
            .filter((lang) => lang.isActive)
            .map((lang) => ({
              code: lang.code,
              name: lang.name,
              nativeName: lang.name, // Using name as nativeName for now
            }))}
          isSubmitting={isSubmittingContent}
          title={
            editingContent
              ? t("admin.hotspotsPage.contentForm.editTitle", "Edit Content")
              : t("admin.hotspotsPage.contentForm.addTitle", "Add Content")
          }
          description={
            editingContent
              ? t(
                  "admin.hotspotsPage.contentForm.editDescription",
                  "Update the content and styling details below."
                )
              : t(
                  "admin.hotspotsPage.contentForm.addDescription",
                  "Add content and styling for hotspot in {panorama}.",
                  {
                    panorama: selectedHotspotForContent?.panorama.title,
                  }
                )
          }
        />

        {/* Multi-Product Manager */}
        {selectedHotspotForProducts && (
          <MultiProductManager
            isOpen={showMultiProductManager}
            onClose={() => {
              setShowMultiProductManager(false);
              setSelectedHotspotForProducts(null);
            }}
            hotspotTitle={`${selectedHotspotForProducts.panorama.title} - Hotspot`}
            products={selectedHotspotForProducts.content || []}
            languages={languages
              .filter((lang) => lang.isActive)
              .map((lang) => ({
                code: lang.code,
                name: lang.name,
                nativeName: lang.name,
              }))}
            onProductAdd={handleProductAdd}
            onProductUpdate={handleProductUpdate}
            onProductDelete={handleProductDelete}
            onProductReorder={handleProductReorder}
          />
        )}
      </div>
    </AdminLayout>
  );
};

export default HotspotsExtendedPage;
