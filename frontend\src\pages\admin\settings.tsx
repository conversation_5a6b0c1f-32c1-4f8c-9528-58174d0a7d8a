// frontend\src\pages\admin\settings.tsx

import React, { useState, useEffect } from "react";
import AdminLayout from "@/components/AdminLayout";
import { toast } from "react-hot-toast";
import {
  Settings as SettingsIcon,
  Shield,
  Database,
  Globe,
  Save,
  RefreshCw,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { Separator } from "@/components/ui/separator";
import { useAppTranslation } from "@/hooks/useAppTranslation";

const SettingsPage: React.FC = () => {
  const { t } = useAppTranslation();
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState({
    // General Settings
    siteName: "IduView",
    siteDescription: "360° Panorama Management System",
    defaultLanguage: "en",

    // System Settings (merged into General)
    maxFileSize: "10",
    allowedFormats: ["jpg", "jpeg", "png"],
    cacheEnabled: true,
    viewerHeight: "600",
  });

  const handleSave = async (section: string) => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      toast.success(
        t(
          "admin.settingsPage.messages.saveSuccess",
          "{section} settings saved successfully",
          { section }
        )
      );
    } catch (error) {
      toast.error(
        t("admin.settingsPage.messages.saveFailed", "Failed to save settings")
      );
    } finally {
      setLoading(false);
    }
  };

  const handleReset = (section: string) => {
    if (
      confirm(
        t(
          "admin.settingsPage.messages.resetConfirm",
          "Are you sure you want to reset {section} settings to default?",
          { section }
        )
      )
    ) {
      // Reset logic here
      toast.success(
        t(
          "admin.settingsPage.messages.resetSuccess",
          "{section} settings reset to default",
          { section }
        )
      );
    }
  };

  const updateSetting = (key: string, value: any) => {
    setSettings((prev) => ({ ...prev, [key]: value }));
  };

  return (
    <AdminLayout title={t("admin.settingsPage.title", "Settings")}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">
            {t("admin.settingsPage.title", "Settings")}
          </h2>
          <p className="text-muted-foreground">
            {t(
              "admin.settingsPage.subtitle",
              "Configure your IduView system preferences and options"
            )}
          </p>
        </div>

        {/* General Settings */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <SettingsIcon className="h-5 w-5" />
                {t("admin.settingsPage.general.title", "General Settings")}
              </CardTitle>
              <CardDescription>
                {t(
                  "admin.settingsPage.general.subtitle",
                  "Configure your IduView system preferences and options"
                )}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Basic Settings */}
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="siteName">
                    {t("admin.settingsPage.general.siteName", "Site Name")}
                  </Label>
                  <Input
                    id="siteName"
                    value={settings.siteName}
                    onChange={(e) => updateSetting("siteName", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="defaultLanguage">
                    {t(
                      "admin.settingsPage.general.defaultLanguage",
                      "Default Language"
                    )}
                  </Label>
                  <Select
                    value={settings.defaultLanguage}
                    onValueChange={(value) =>
                      updateSetting("defaultLanguage", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">
                        {t(
                          "admin.settingsPage.general.languages.english",
                          "English"
                        )}
                      </SelectItem>
                      <SelectItem value="da">
                        {t(
                          "admin.settingsPage.general.languages.danish",
                          "Danish"
                        )}
                      </SelectItem>
                      <SelectItem value="de">
                        {t(
                          "admin.settingsPage.general.languages.german",
                          "German"
                        )}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="siteDescription">
                  {t(
                    "admin.settingsPage.general.siteDescription",
                    "Site Description"
                  )}
                </Label>
                <Textarea
                  id="siteDescription"
                  value={settings.siteDescription}
                  onChange={(e) =>
                    updateSetting("siteDescription", e.target.value)
                  }
                  rows={3}
                />
              </div>

              <Separator />

              {/* System Settings */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Database className="h-5 w-5" />
                  <h3 className="text-lg font-medium">
                    {t(
                      "admin.settingsPage.system.title",
                      "System Configuration"
                    )}
                  </h3>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="maxFileSize">
                      {t(
                        "admin.settingsPage.system.maxFileSize",
                        "Max File Size (MB)"
                      )}
                    </Label>
                    <Input
                      id="maxFileSize"
                      type="number"
                      value={settings.maxFileSize}
                      onChange={(e) =>
                        updateSetting("maxFileSize", e.target.value)
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="viewerHeight">
                      {t(
                        "admin.settingsPage.system.viewerHeight",
                        "Default Viewer Height (px)"
                      )}
                    </Label>
                    <Input
                      id="viewerHeight"
                      type="number"
                      value={settings.viewerHeight}
                      onChange={(e) =>
                        updateSetting("viewerHeight", e.target.value)
                      }
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>
                        {t(
                          "admin.settingsPage.system.cacheEnabled.label",
                          "Cache Enabled"
                        )}
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        {t(
                          "admin.settingsPage.system.cacheEnabled.description",
                          "Enable caching for better performance"
                        )}
                      </p>
                    </div>
                    <Switch
                      checked={settings.cacheEnabled}
                      onCheckedChange={(checked) =>
                        updateSetting("cacheEnabled", checked)
                      }
                    />
                  </div>
                </div>
              </div>

              <Separator />

              <div className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={() =>
                    handleReset(t("admin.settingsPage.tabs.general", "General"))
                  }
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  {t(
                    "admin.settingsPage.actions.resetToDefault",
                    "Reset to Default"
                  )}
                </Button>
                <Button
                  onClick={() =>
                    handleSave(t("admin.settingsPage.tabs.general", "General"))
                  }
                  disabled={loading}
                >
                  <Save className="mr-2 h-4 w-4" />
                  {t("admin.settingsPage.actions.saveChanges", "Save Changes")}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* System Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              {t("admin.settingsPage.systemInfo.title", "System Information")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div>
                <Label className="text-sm font-medium">
                  {t("admin.settingsPage.systemInfo.version", "Version")}
                </Label>
                <p className="text-sm text-muted-foreground">1.0.0</p>
              </div>
              <div>
                <Label className="text-sm font-medium">
                  {t(
                    "admin.settingsPage.systemInfo.environment",
                    "Environment"
                  )}
                </Label>
                <p className="text-sm text-muted-foreground">
                  {t(
                    "admin.settingsPage.systemInfo.development",
                    "Development"
                  )}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium">
                  {t("admin.settingsPage.systemInfo.database", "Database")}
                </Label>
                <p className="text-sm text-muted-foreground">PostgreSQL</p>
              </div>
              <div>
                <Label className="text-sm font-medium">
                  {t(
                    "admin.settingsPage.systemInfo.lastUpdated",
                    "Last Updated"
                  )}
                </Label>
                <p className="text-sm text-muted-foreground">
                  {new Date().toLocaleDateString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default SettingsPage;
