import React, { useState } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";

interface HotspotContent {
  title: string;
  description: string;
  imageUrl?: string;
  linkUrl?: string;
  linkText?: string;
}

interface Hotspot {
  id: string;
  xPosition: number;
  yPosition: number;
  iconType: string;
  iconColor: string;
  content: HotspotContent[];
}

interface HotspotPopupProps {
  hotspot: Hotspot;
  isOpen: boolean;
  onClose: () => void;
  language?: string;
}

const HotspotPopup: React.FC<HotspotPopupProps> = ({
  hotspot,
  isOpen,
  onClose,
  language = "en",
}) => {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  if (!isOpen) return null;

  // Get content for current language or fallback to first available
  const content = hotspot.content.find((c) => c.title) || hotspot.content[0];

  if (!content) {
    return null;
  }

  const handleImageLoad = () => {
    setImageLoading(false);
  };

  const handleImageError = () => {
    setImageLoading(false);
    setImageError(true);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-end">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />

      {/* Popup Panel */}
      <div className="relative w-full max-w-md h-full bg-white shadow-2xl transform transition-transform duration-300 ease-in-out">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900 truncate">
            {content.title}
          </h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {/* Image */}
          {content.imageUrl && !imageError && (
            <div className="relative">
              {imageLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              )}
              <img
                src={content.imageUrl}
                alt={content.title}
                className={`w-full h-48 object-cover rounded-lg shadow-md transition-opacity duration-300 ${
                  imageLoading ? "opacity-0" : "opacity-100"
                }`}
                onLoad={handleImageLoad}
                onError={handleImageError}
              />
            </div>
          )}

          {/* Description */}
          <div className="prose prose-sm max-w-none">
            <div
              className="text-gray-700 leading-relaxed"
              dangerouslySetInnerHTML={{ __html: content.description }}
            />
          </div>

          {/* Link */}
          {content.linkUrl && content.linkText && (
            <div className="pt-4 border-t border-gray-200">
              <a
                href={content.linkUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
              >
                {content.linkText}
                <svg
                  className="ml-2 w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                  />
                </svg>
              </a>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between text-sm text-gray-500">
            <span>Hotspot Information</span>
            <div className="flex items-center space-x-2">
              <div
                className="w-3 h-3 rounded-full border border-white"
                style={{ backgroundColor: hotspot.iconColor }}
              />
              <span className="capitalize">{hotspot.iconType}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HotspotPopup;
