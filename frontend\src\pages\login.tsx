// frontend\src\pages\login.tsx

import React, { useState } from "react";
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import axios from "axios";
import { toast } from "react-hot-toast";
import { Eye, EyeOff, LogIn, Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import DebugEnv from "@/components/DebugEnv";
import { LanguageToggle } from "@/components/LanguageToggle";
import { useAppTranslation } from "@/hooks/useAppTranslation";

// We'll create the schema inside the component to access translations

type LoginFormData = {
  email: string;
  password: string;
};

const LoginPage: React.FC = () => {
  const { t } = useAppTranslation();
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Create schema with translations
  const schema = yup.object({
    email: yup
      .string()
      .email(t("login.validation.emailInvalid", "Invalid email"))
      .required(t("login.validation.emailRequired", "Email is required")),
    password: yup
      .string()
      .min(
        6,
        t(
          "login.validation.passwordMinLength",
          "Password must be at least 6 characters"
        )
      )
      .required(t("login.validation.passwordRequired", "Password is required")),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: yupResolver(schema),
  });

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    try {
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL}/api/auth/login`,
        data
      );

      if (response.data.success) {
        // Store token in localStorage
        localStorage.setItem("token", response.data.data.token);
        localStorage.setItem("user", JSON.stringify(response.data.data.user));

        toast.success(t("login.success", "Login successful!"));
        router.push("/admin");
      }
    } catch (error: any) {
      const message =
        error.response?.data?.error?.message ||
        t("login.failed", "Login failed");
      toast.error(message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted flex items-center justify-center p-4">
      {/* Language Toggle - Top Right */}
      <div className="absolute top-4 right-4 z-10">
        <LanguageToggle />
      </div>

      <div className="w-full max-w-md space-y-8">
        {/* Header */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-lg">
                I
              </span>
            </div>
            <h1 className="text-3xl font-bold tracking-tight">IduView</h1>
          </div>
          <p className="text-muted-foreground">
            {t("login.subtitle", "360° Panorama Management System")}
          </p>
        </div>

        {/* Login Card */}
        <Card className="border-0 shadow-2xl">
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-2xl font-semibold tracking-tight">
              {t("login.title", "Admin Login")}
            </CardTitle>
            <CardDescription>
              {t("login.description", "Sign in to your account to continue")}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {/* Email Field */}
              <div className="space-y-2">
                <Label htmlFor="email">{t("login.email", "Email")}</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder={t("login.emailPlaceholder", "<EMAIL>")}
                  autoComplete="email"
                  {...register("email")}
                  className={errors.email ? "border-destructive" : ""}
                />
                {errors.email && (
                  <p className="text-sm text-destructive">
                    {errors.email.message}
                  </p>
                )}
              </div>

              {/* Password Field */}
              <div className="space-y-2">
                <Label htmlFor="password">
                  {t("login.password", "Password")}
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder={t(
                      "login.passwordPlaceholder",
                      "Enter your password"
                    )}
                    autoComplete="current-password"
                    {...register("password")}
                    className={`pr-10 ${
                      errors.password ? "border-destructive" : ""
                    }`}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <Eye className="h-4 w-4 text-muted-foreground" />
                    )}
                  </Button>
                </div>
                {errors.password && (
                  <p className="text-sm text-destructive">
                    {errors.password.message}
                  </p>
                )}
              </div>

              {/* Submit Button */}
              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
                size="lg"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t("login.loggingIn", "Signing in...")}
                  </>
                ) : (
                  <>
                    <LogIn className="mr-2 h-4 w-4" />
                    {t("login.loginButton", "Sign In")}
                  </>
                )}
              </Button>
            </form>

            {/* Demo Credentials */}
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  {t("login.demoCredentials", "Demo Credentials")}
                </span>
              </div>
            </div>

            <Alert>
              <AlertDescription className="text-center">
                <strong>{t("login.demoEmailLabel", "Email:")}</strong>{" "}
                <EMAIL>
                <br />
                <strong>
                  {t("login.demoPasswordLabel", "Password:")}
                </strong>{" "}
                admin123
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* Footer */}
        <p className="text-center text-sm text-muted-foreground">
          {t("login.footer", "Secure admin access for panorama management")}
        </p>
      </div>
      <DebugEnv />
    </div>
  );
};

export default LoginPage;
