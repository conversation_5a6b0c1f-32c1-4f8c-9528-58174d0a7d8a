// frontend\src\components\VisualHotspotEditor.tsx

import React, { useState, useRef, useCallback, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { MapPin, Save, X, Eye, EyeOff } from "lucide-react";
import { Hotspot } from "@/types/hotspot";
import { useAppTranslation } from "@/hooks/useAppTranslation";
import View360, { EquirectProjection, EVENTS } from "@egjs/react-view360";
import "@egjs/react-view360/css/view360.min.css";

interface VisualHotspotEditorProps {
  panoramaUrl: string;
  existingHotspots?: Hotspot[];
  onHotspotPlace: (xPosition: number, yPosition: number) => void;
  onHotspotEdit?: (hotspot: Hotspot) => void;
  onHotspotDelete?: (hotspotId: string) => void;
  isPlacingMode?: boolean;
  onTogglePlacingMode?: () => void;
  onHotspotPlaced?: () => void;
  className?: string;
}

const VisualHotspotEditor: React.FC<VisualHotspotEditorProps> = ({
  panoramaUrl,
  existingHotspots = [],
  onHotspotPlace,
  onHotspotEdit,
  onHotspotDelete,
  isPlacingMode = false,
  onTogglePlacingMode,
  onHotspotPlaced,
  className = "",
}) => {
  const { t } = useAppTranslation();
  const [tempHotspot, setTempHotspot] = useState<Hotspot | null>(null);
  const [showExistingHotspots, setShowExistingHotspots] = useState(true);
  const [draggedHotspot, setDraggedHotspot] = useState<Hotspot | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [updatedHotspots, setUpdatedHotspots] = useState<Hotspot[]>([]);
  const [dragPosition, setDragPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [renderKey, setRenderKey] = useState(0);
  const viewerRef = useRef<any>(null);

  // Create projection with useMemo
  const projection = useMemo(
    () =>
      new EquirectProjection({
        src: panoramaUrl,
      }),
    [panoramaUrl]
  );

  // Convert yaw/pitch to percentage coordinates for our backend
  const convertYawPitchToPercent = (yaw: number, pitch: number) => {
    // Convert yaw (-180 to 180) to x percentage (0 to 100)
    const xPosition = ((yaw + 180) / 360) * 100;
    // Convert pitch (-90 to 90) to y percentage (0 to 100)
    const yPosition = ((90 - pitch) / 180) * 100;

    return { xPosition, yPosition };
  };

  // Convert percentage coordinates to yaw/pitch for View360
  const convertPercentToYawPitch = (xPercent: number, yPercent: number) => {
    // Convert x percentage to yaw
    const yaw = (xPercent / 100) * 360 - 180;
    // Convert y percentage to pitch
    const pitch = 90 - (yPercent / 100) * 180;

    return { yaw, pitch };
  };

  // Convert mouse coordinates to yaw/pitch
  const convertMouseToYawPitch = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      const canvas = event.currentTarget.querySelector("canvas");
      if (!canvas || !viewerRef.current) return null;

      const rect = canvas.getBoundingClientRect();
      const viewer = viewerRef.current;
      const camera = viewer.camera;

      if (!camera) return null;

      // Get mouse position relative to canvas
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      // Convert to normalized coordinates (0-1)
      const normalizedX = x / rect.width;
      const normalizedY = y / rect.height;

      // Get current camera properties
      const currentYaw = camera.yaw;
      const currentPitch = camera.pitch;
      const currentZoom = camera.zoom;
      const fov = camera.fov;

      // Calculate the field of view in radians
      const horizontalFov = (fov / currentZoom) * (Math.PI / 180);
      const verticalFov = horizontalFov * (rect.height / rect.width);

      // Convert normalized coordinates to offset from center
      const offsetX = (normalizedX - 0.5) * horizontalFov;
      const offsetY = (0.5 - normalizedY) * verticalFov; // Flip Y axis

      // Calculate the yaw and pitch based on current camera position and offset
      // View360 uses right-handed coordinate system:
      // - Positive yaw rotates counter-clockwise, so we need to invert the X offset
      // - Positive pitch looks up, negative looks down (Y offset is already flipped)
      const yaw = currentYaw - offsetX * (180 / Math.PI); // Invert X for right-handed system
      const pitch = currentPitch + offsetY * (180 / Math.PI);

      // Clamp pitch to valid range (-90 to 90)
      const clampedPitch = Math.max(-90, Math.min(90, pitch));

      return { yaw, pitch: clampedPitch };
    },
    []
  );

  // Handle manual click for hotspot placement
  const handlePanoramaClick = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      if (!isPlacingMode || isDragging) return;

      // Prevent event bubbling
      event.preventDefault();
      event.stopPropagation();

      const coordinates = convertMouseToYawPitch(event);
      if (!coordinates) return;

      const { yaw, pitch } = coordinates;

      // Convert to our percentage system for backend storage
      const { xPosition, yPosition } = convertYawPitchToPercent(yaw, pitch);

      console.log("🎯 Hotspot placed at click coordinates:", {
        yaw,
        pitch,
        xPosition,
        yPosition,
      });

      // Create temporary hotspot for immediate preview
      const newTempHotspot: Hotspot = {
        id: `temp-${Date.now()}`,
        xPosition,
        yPosition,
        iconType: "info",
        iconColor: "#007bff",
        panoramaId: "temp",
        isTemporary: true,
      };

      setTempHotspot(newTempHotspot);

      // Force re-render to show the new hotspot immediately
      setRenderKey((prev) => prev + 1);
    },
    [isPlacingMode, isDragging, convertMouseToYawPitch]
  );

  // Handle hotspot drag start
  const handleHotspotMouseDown = useCallback(
    (hotspot: Hotspot, event: React.MouseEvent) => {
      event.preventDefault();
      event.stopPropagation();
      setDraggedHotspot(hotspot);
      setIsDragging(true);

      // Set initial drag position
      const rect = event.currentTarget.getBoundingClientRect();
      setDragPosition({
        x: event.clientX - rect.left,
        y: event.clientY - rect.top,
      });
    },
    []
  );

  // Handle hotspot drag
  const handleHotspotDrag = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      if (!isDragging || !draggedHotspot) return;

      event.preventDefault();
      event.stopPropagation();

      // Update drag position for visual feedback
      const canvas = event.currentTarget.querySelector("canvas");
      if (canvas) {
        const rect = canvas.getBoundingClientRect();
        setDragPosition({
          x: event.clientX - rect.left,
          y: event.clientY - rect.top,
        });
      }

      const coordinates = convertMouseToYawPitch(event);
      if (!coordinates) return;

      const { yaw, pitch } = coordinates;
      const { xPosition, yPosition } = convertYawPitchToPercent(yaw, pitch);

      // Update the dragged hotspot position
      if (draggedHotspot.isTemporary) {
        // Update temporary hotspot
        setTempHotspot({
          ...draggedHotspot,
          xPosition,
          yPosition,
        });
      } else {
        // Update existing hotspot position in local state
        const updatedHotspot = {
          ...draggedHotspot,
          xPosition,
          yPosition,
        };

        // Update the updatedHotspots array
        setUpdatedHotspots((prev) => {
          const existing = prev.find((h) => h.id === draggedHotspot.id);
          if (existing) {
            return prev.map((h) =>
              h.id === draggedHotspot.id ? updatedHotspot : h
            );
          } else {
            return [...prev, updatedHotspot];
          }
        });

        // Update the draggedHotspot reference for continued dragging
        setDraggedHotspot(updatedHotspot);
      }
    },
    [isDragging, draggedHotspot, convertMouseToYawPitch]
  );

  // Handle hotspot drag end
  const handleHotspotMouseUp = useCallback(async () => {
    const currentDraggedHotspot = draggedHotspot;
    const wasDragging = isDragging;

    console.log("🔚 Drag ended:", {
      wasDragging,
      draggedHotspot: currentDraggedHotspot,
      updatedHotspotsCount: updatedHotspots.length,
      updatedHotspots: updatedHotspots.map((h) => ({
        id: h.id,
        x: h.xPosition,
        y: h.yPosition,
      })),
    });

    if (
      wasDragging &&
      currentDraggedHotspot &&
      !currentDraggedHotspot.isTemporary
    ) {
      // Clear drag state to show the hotspot at new position
      setIsDragging(false);
      setDraggedHotspot(null);
      setDragPosition(null);

      // Force a re-render by incrementing the render key
      setRenderKey((prev) => prev + 1);

      // Save the updated position to backend
      try {
        const token = localStorage.getItem("token");
        const headers = { Authorization: `Bearer ${token}` };

        await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/hotspots/${currentDraggedHotspot.id}`,
          {
            method: "PUT",
            headers: {
              ...headers,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              xPosition: currentDraggedHotspot.xPosition,
              yPosition: currentDraggedHotspot.yPosition,
            }),
          }
        );

        console.log("✅ Hotspot position saved:", {
          id: currentDraggedHotspot.id,
          xPosition: currentDraggedHotspot.xPosition,
          yPosition: currentDraggedHotspot.yPosition,
        });
      } catch (error) {
        console.error("❌ Failed to save hotspot position:", error);
        // Optionally show a toast notification

        // If save failed, remove the hotspot from updatedHotspots to revert to original position
        setUpdatedHotspots((prev) =>
          prev.filter((h) => h.id !== currentDraggedHotspot.id)
        );
      }
    } else {
      // Clear drag state for temporary hotspots or non-drag scenarios
      setIsDragging(false);
      setDraggedHotspot(null);
      setDragPosition(null);
    }
  }, [isDragging, draggedHotspot, updatedHotspots]);

  // Confirm hotspot placement
  const handleConfirmPlacement = () => {
    if (tempHotspot) {
      // Call the parent's hotspot placement handler
      onHotspotPlace(tempHotspot.xPosition, tempHotspot.yPosition);

      // Clear temporary hotspot
      setTempHotspot(null);

      // Force re-render after a short delay to ensure parent state is updated
      setTimeout(() => {
        setRenderKey((prev) => prev + 1);

        // Trigger parent refresh if available
        if (onHotspotPlaced) {
          onHotspotPlaced();
        }
      }, 100);

      if (onTogglePlacingMode) {
        onTogglePlacingMode();
      }
    }
  };

  // Cancel hotspot placement
  const handleCancelPlacement = () => {
    setTempHotspot(null);
    if (onTogglePlacingMode) {
      onTogglePlacingMode();
    }
  };

  // Combine existing hotspots with temporary hotspot and updated positions for display
  const allHotspots = [
    ...(showExistingHotspots
      ? existingHotspots.map((hotspot) => {
          // Check if this hotspot has been updated by dragging
          const updated = updatedHotspots.find((u) => u.id === hotspot.id);
          const result = updated || hotspot;

          // Debug logging
          if (updated) {
            console.log("🔄 Using updated hotspot:", {
              id: hotspot.id,
              original: { x: hotspot.xPosition, y: hotspot.yPosition },
              updated: { x: updated.xPosition, y: updated.yPosition },
            });
          }

          return result;
        })
      : []),
    ...(tempHotspot ? [tempHotspot] : []),
  ];

  // Force re-render when drag state changes
  React.useEffect(() => {
    if (!isDragging && updatedHotspots.length > 0) {
      console.log("🔄 Drag ended, forcing re-render with updated hotspots");
    }
  }, [isDragging, updatedHotspots.length]);

  // Force re-render when existingHotspots prop changes (new hotspot added)
  React.useEffect(() => {
    console.log("🔄 Existing hotspots changed, forcing re-render");
    setRenderKey((prev) => prev + 1);
  }, [existingHotspots.length]);

  return (
    <>
      <style jsx>{`
        /* Exact working test styles - NO TRANSFORMS */
        .search {
          width: 24px;
          height: 24px;
          position: relative;
          cursor: pointer;
          user-select: none;
          -webkit-user-drag: none;
        }

        /* Main white hotspot circle */
        .search:before {
          position: absolute;
          content: "";
          width: 20px;
          height: 20px;
          top: 2px;
          left: 2px;
          border-radius: 50%;
          background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
          border: 2px solid #343a40;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15),
            0 2px 4px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.8);
        }

        /* Temporary hotspot styling */
        .temporary-hotspot {
          background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
          border: 2px solid #ffffff;
          box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3),
            0 2px 4px rgba(0, 123, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
        }

        /* First pulsing ring */
        .search:after {
          position: absolute;
          content: "";
          top: -8px;
          left: -8px;
          right: -8px;
          bottom: -8px;
          border: 2px solid rgba(52, 58, 64, 0.6);
          border-radius: 50%;
          animation: pulse-ring 2s infinite ease-out;
        }

        .search.temporary-hotspot:after {
          border: 2px solid rgba(0, 123, 255, 0.8);
        }

        /* Second pulsing ring */
        .search .hotspot-inner:before {
          position: absolute;
          content: "";
          top: -32px;
          left: -32px;
          right: -32px;
          bottom: -32px;
          border: 1px solid rgba(52, 58, 64, 0.3);
          border-radius: 50%;
          animation: pulse-ring 2s infinite ease-out 0.5s;
        }

        .search.temporary-hotspot .hotspot-inner:before {
          border: 1px solid rgba(0, 123, 255, 0.5);
        }

        @keyframes pulse-ring {
          0% {
            transform: scale(0.8);
            opacity: 1;
          }
          50% {
            transform: scale(1.2);
            opacity: 0.7;
          }
          100% {
            transform: scale(1.6);
            opacity: 0;
          }
        }

        .search:hover:before {
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25),
            0 4px 8px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.9);
        }

        .search:hover:after,
        .search:hover .hotspot-inner:before {
          animation-duration: 1s;
        }

        /* Centered inner dot - NO TRANSFORM */
        .search .hotspot-inner {
          position: absolute;
          top: 8px;
          left: 8px;
          width: 8px;
          height: 8px;
          background: #343a40;
          border-radius: 50%;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
          z-index: 2;
        }

        .temporary-hotspot .hotspot-inner {
          background: #ffffff;
        }

        /* Original hotspot being dragged (faded) */
        .view360-hotspot.being-dragged {
          opacity: 0.3 !important;
        }

        /* Drag overlay hotspot (follows mouse) - NO TRANSFORM */
        .search.dragging {
          z-index: 30 !important;
          opacity: 1 !important;
          visibility: visible !important;
          pointer-events: none !important;
        }

        .search.dragging:before {
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3),
            0 4px 10px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
          transform: scale(1.2);
        }

        .search.dragging:after {
          transform: scale(1.2);
        }

        .view360-hotspot.dragging::before,
        .view360-hotspot.dragging::after {
          animation-duration: 0.5s;
          border-color: rgba(0, 123, 255, 1) !important;
          opacity: 1 !important;
        }

        /* Ensure dragging hotspot stays visible */
        .view360-hotspot.dragging .hotspot-inner {
          opacity: 1 !important;
          visibility: visible !important;
        }

        /* Panorama viewer styling */
        .is-16by9 {
          width: 100%;
          height: 70vh;
        }
      `}</style>

      <div className={`space-y-4 ${className}`}>
        {/* Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center">
                <MapPin className="mr-2 h-5 w-5" />
                {t("admin.visualHotspotEditor.title", "Visual Hotspot Editor")}
              </span>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowExistingHotspots(!showExistingHotspots)}
                >
                  {showExistingHotspots ? (
                    <EyeOff className="mr-2 h-4 w-4" />
                  ) : (
                    <Eye className="mr-2 h-4 w-4" />
                  )}
                  {showExistingHotspots
                    ? t(
                        "admin.visualHotspotEditor.hideExisting",
                        "Hide Existing"
                      )
                    : t(
                        "admin.visualHotspotEditor.showExisting",
                        "Show Existing"
                      )}
                </Button>
                {onTogglePlacingMode && (
                  <Button
                    variant={isPlacingMode ? "destructive" : "default"}
                    size="sm"
                    onClick={onTogglePlacingMode}
                  >
                    {isPlacingMode ? (
                      <>
                        <X className="mr-2 h-4 w-4" />
                        {t(
                          "admin.visualHotspotEditor.cancelPlacing",
                          "Cancel Placing"
                        )}
                      </>
                    ) : (
                      <>
                        <MapPin className="mr-2 h-4 w-4" />
                        {t(
                          "admin.visualHotspotEditor.placeHotspot",
                          "Place Hotspot"
                        )}
                      </>
                    )}
                  </Button>
                )}
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Badge variant={isPlacingMode ? "default" : "secondary"}>
                  {isPlacingMode
                    ? t(
                        "admin.visualHotspotEditor.placingModeActive",
                        "Placing Mode Active"
                      )
                    : t("admin.visualHotspotEditor.viewMode", "View Mode")}
                </Badge>
                {existingHotspots.length > 0 && (
                  <span className="text-sm text-muted-foreground">
                    {existingHotspots.length === 1
                      ? t(
                          "admin.visualHotspotEditor.existingHotspot",
                          "{count} existing hotspot",
                          { count: existingHotspots.length }
                        )
                      : t(
                          "admin.visualHotspotEditor.existingHotspots",
                          "{count} existing hotspots",
                          { count: existingHotspots.length }
                        )}
                  </span>
                )}
              </div>
              {tempHotspot && (
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground">
                    {t(
                      "admin.visualHotspotEditor.position",
                      "Position: {x}%, {y}%",
                      {
                        x: tempHotspot.xPosition.toFixed(1),
                        y: tempHotspot.yPosition.toFixed(1),
                      }
                    )}
                  </span>
                  <Button size="sm" onClick={handleConfirmPlacement}>
                    <Save className="mr-2 h-4 w-4" />
                    {t("admin.visualHotspotEditor.confirm", "Confirm")}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCancelPlacement}
                  >
                    <X className="mr-2 h-4 w-4" />
                    {t("admin.visualHotspotEditor.cancel", "Cancel")}
                  </Button>
                </div>
              )}
            </div>
            {isPlacingMode && !tempHotspot && (
              <p className="text-sm text-muted-foreground mt-2">
                {t(
                  "admin.visualHotspotEditor.clickToPlace",
                  "Click anywhere on the panorama to place a hotspot"
                )}
              </p>
            )}
          </CardContent>
        </Card>

        {/* Panorama Viewer with Click Handler */}
        <div className="relative">
          <div
            className={`relative ${
              isPlacingMode ? "cursor-crosshair" : "cursor-default"
            }`}
            onClick={handlePanoramaClick}
            onMouseMove={handleHotspotDrag}
            onMouseUp={handleHotspotMouseUp}
          >
            <View360
              key={`view360-${renderKey}`}
              ref={viewerRef}
              className="is-16by9"
              projection={projection}
              autoplay={false}
              rotate={true}
              zoom={true}
              style={{ height: "70vh" }}
            >
              {/* Hotspot container */}
              <div key={`hotspots-${renderKey}`} className="view360-hotspots">
                {allHotspots.map((hotspot, idx) => {
                  const { yaw, pitch } = convertPercentToYawPitch(
                    hotspot.xPosition,
                    hotspot.yPosition
                  );

                  const isBeingDragged =
                    isDragging && draggedHotspot?.id === hotspot.id;

                  return (
                    <div
                      key={idx}
                      className={`view360-hotspot search ${
                        hotspot.isTemporary ? "temporary-hotspot" : ""
                      } ${isBeingDragged ? "being-dragged" : ""}`}
                      data-yaw={yaw}
                      data-pitch={pitch}
                      onMouseDown={(e) => handleHotspotMouseDown(hotspot, e)}
                      onClick={(e) => {
                        if (
                          !isDragging &&
                          !hotspot.isTemporary &&
                          onHotspotEdit
                        ) {
                          e.preventDefault();
                          e.stopPropagation();
                          onHotspotEdit(hotspot);
                        }
                      }}
                      title={
                        hotspot.isTemporary
                          ? "New Hotspot - Drag to reposition"
                          : "Click to edit, drag to move"
                      }
                      style={{
                        cursor: isBeingDragged ? "grabbing" : "grab",
                        opacity: isBeingDragged ? 0.3 : 1,
                      }}
                    >
                      <div className="hotspot-inner" />
                    </div>
                  );
                })}
              </div>
            </View360>
          </div>

          {/* Drag overlay - shows hotspot following mouse during drag */}
          {isDragging && draggedHotspot && dragPosition && (
            <div
              className="absolute pointer-events-none z-30"
              style={{
                left: dragPosition.x,
                top: dragPosition.y,
                transform: "translate(-50%, -50%)",
              }}
            >
              <div className="view360-hotspot dragging">
                <div className="hotspot-inner" />
              </div>
            </div>
          )}

          {/* Overlay for placing mode */}
          {isPlacingMode && (
            <div className="absolute inset-0 bg-blue-500/10 border-2 border-blue-500 border-dashed rounded-lg pointer-events-none flex items-center justify-center">
              <div className="bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg">
                <MapPin className="inline mr-2 h-4 w-4" />
                {t(
                  "admin.visualHotspotEditor.clickToPlaceOverlay",
                  "Click to place hotspot"
                )}
              </div>
            </div>
          )}
        </div>

        {/* Instructions */}
        <Card>
          <CardContent className="pt-6">
            <div className="text-sm text-muted-foreground space-y-2">
              <p>
                <strong>
                  {t(
                    "admin.visualHotspotEditor.instructions.title",
                    "Instructions:"
                  )}
                </strong>
              </p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>
                  {t(
                    "admin.visualHotspotEditor.instructions.placeMode",
                    'Click "Place Hotspot" to enter placing mode'
                  )}
                </li>
                <li>
                  {t(
                    "admin.visualHotspotEditor.instructions.clickPanorama",
                    "Click anywhere on the panorama to position the hotspot"
                  )}
                </li>
                <li>
                  {t(
                    "admin.visualHotspotEditor.instructions.confirmPosition",
                    "Confirm the position or cancel to try again"
                  )}
                </li>
                <li>
                  {t(
                    "admin.visualHotspotEditor.instructions.editExisting",
                    "Click existing hotspots to edit their content"
                  )}
                </li>
                <li>
                  {t(
                    "admin.visualHotspotEditor.instructions.toggleVisibility",
                    'Use "Show/Hide Existing" to toggle hotspot visibility'
                  )}
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
};

export default VisualHotspotEditor;
