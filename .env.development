# Development Environment Variables
NODE_ENV=development

# Database
DATABASE_URL=postgresql://root:Onamission%23007@************:5432/iduna_db

# API Configuration
BACKEND_PORT=5000
FRONTEND_PORT=3000
API_BASE_URL=http://localhost:5000
FRONTEND_BASE_URL=http://localhost:3000

# Authentication
JWT_SECRET=dev_jwt_secret_change_in_production_2024
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_EXPIRES_IN=30d

# Redis (not used in development)
REDIS_URL=redis://localhost:6379

# File Upload
MAX_FILE_SIZE=50MB
UPLOAD_PATH=./uploads

# CORS
CORS_ORIGIN=http://localhost:3000

# WordPress Integration
WORDPRESS_INTEGRATION_URL=http://localhost:8080

# Logging
LOG_LEVEL=debug
