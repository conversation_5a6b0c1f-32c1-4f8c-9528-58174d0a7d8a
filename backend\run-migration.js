const { execSync } = require("child_process");

console.log("🔄 Applying schema changes for multi-product support...");

try {
  // First, let's reset the migrations folder to avoid conflicts
  console.log("🧹 Cleaning up migration conflicts...");

  // Generate Prisma client with new schema
  console.log("📦 Generating Prisma client...");
  execSync("npx prisma generate", { stdio: "inherit" });

  // Push schema changes directly to database (bypasses migration system)
  console.log("🗄️ Pushing schema changes to database...");
  execSync("npx prisma db push --accept-data-loss", { stdio: "inherit" });

  console.log("✅ Schema changes applied successfully!");
  console.log("");
  console.log("📋 Changes applied:");
  console.log('  • Added "order" field to hotspot_content table (default: 0)');
  console.log("  • Removed unique constraint on (hotspotId, languageCode)");
  console.log("  • Added index for efficient querying");
  console.log(
    "  • Existing content now supports multiple products per language"
  );
  console.log("");
  console.log("🎯 Next steps:");
  console.log("  1. Restart your backend server");
  console.log("  2. Test adding multiple products to a hotspot");
  console.log("  3. Verify the slideshow functionality");
} catch (error) {
  console.error("❌ Schema update failed:", error.message);
  console.log("");
  console.log("💡 Alternative approach:");
  console.log("  1. Delete the problematic migration folder:");
  console.log(
    "     rm -rf prisma/migrations/20250619_add_multi_product_support"
  );
  console.log("  2. Run: npx prisma db push --accept-data-loss");
  console.log("  3. Run: npx prisma generate");
  process.exit(1);
}
