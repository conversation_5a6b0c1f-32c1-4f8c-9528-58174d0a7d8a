// frontend\src\components\EnhancedHotspotContentForm.tsx

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
// import { useTranslation } from "next-i18next";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { FileUpload } from "@/components/ui/file-upload";
import ContentFileUpload from "@/components/ContentFileUpload";
import { Separator } from "@/components/ui/separator";
import { DynamicHotspotDialog } from "@/components/DynamicHotspotDialog";
import {
  Bold,
  Italic,
  Underline,
  Type,
  Link as LinkIcon,
  Image as ImageIcon,
} from "lucide-react";
import { HotspotContent } from "@/types/hotspot";
import { RichTextEditor } from "@/components/ui/rich-text-editor";

interface Language {
  code: string;
  name: string;
  nativeName: string;
}

interface EnhancedHotspotContentFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: HotspotContent) => void;
  initialData?: Partial<HotspotContent>;
  languages: Language[];
  isSubmitting?: boolean;
  title?: string;
  description?: string;
  selectedLanguage?: string; // For pre-selecting language in multi-product mode
}

const fontFamilies = [
  { value: "Inter, sans-serif", label: "Inter (Default)" },
  { value: "Arial, sans-serif", label: "Arial" },
  { value: "Helvetica, sans-serif", label: "Helvetica" },
  { value: "Georgia, serif", label: "Georgia" },
  { value: "Times New Roman, serif", label: "Times New Roman" },
  { value: "Courier New, monospace", label: "Courier New" },
  { value: "Roboto, sans-serif", label: "Roboto" },
  { value: "Open Sans, sans-serif", label: "Open Sans" },
];

const fontSizes = [
  { value: "12px", label: "Extra Small" },
  { value: "14px", label: "Small" },
  { value: "16px", label: "Medium" },
  { value: "18px", label: "Large" },
  { value: "20px", label: "Extra Large" },
  { value: "24px", label: "XXL" },
  { value: "28px", label: "XXXL" },
];

const fontWeights = [
  { value: "300", label: "Light" },
  { value: "400", label: "Normal" },
  { value: "500", label: "Medium" },
  { value: "600", label: "Semi Bold" },
  { value: "700", label: "Bold" },
  { value: "800", label: "Extra Bold" },
];

export const EnhancedHotspotContentForm: React.FC<
  EnhancedHotspotContentFormProps
> = ({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  languages,
  isSubmitting = false,
  title = "Add Hotspot Content",
  description = "Configure the content and styling for this hotspot.",
  selectedLanguage,
}) => {
  // Simple fallback for translation
  const t = (key: string, fallback?: string) => fallback || key;
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string>("");
  const [fileContent, setFileContent] = useState<
    | {
        fileUrl: string;
        fileType: "image" | "pdf";
        metadata: any;
      }
    | undefined
  >(undefined);

  const contentSchema = yup.object({
    title: yup.string().required("Title is required"),
    subtitle: yup.string(),
    description: yup.string().required("Description is required"),
    descriptionJson: yup.mixed().optional(),
    descriptionHtml: yup.string().optional(),
    imageUrl: yup.string().url("Must be a valid URL"),
    linkUrl: yup.string().url("Must be a valid URL"),
    linkText: yup.string(),
    languageCode: yup.string().required("Language is required"),
  });

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<HotspotContent>({
    resolver: yupResolver(contentSchema),
    defaultValues: {
      title: "",
      subtitle: "",
      description: "",
      descriptionJson: undefined,
      descriptionHtml: "",
      imageUrl: "",
      linkUrl: "",
      linkText: "",
      languageCode: selectedLanguage || initialData?.languageCode || "en",
      contentType: "standard",
      fileUrl: "",
      fileType: undefined,
      fileMetadata: undefined,
      titleStyle: {
        fontSize: "18px",
        fontWeight: "600",
        fontStyle: "normal",
        textDecoration: "none",
        fontFamily: "Inter, sans-serif",
        color: "#000000",
        textAlign: "left",
        paddingTop: "0px",
        paddingBottom: "8px",
      },
      subtitleStyle: {
        fontSize: "14px",
        fontWeight: "400",
        fontStyle: "normal",
        textDecoration: "none",
        fontFamily: "Inter, sans-serif",
        color: "#666666",
        textAlign: "left",
        paddingTop: "0px",
        paddingBottom: "8px",
      },
      descriptionStyle: {
        fontSize: "16px",
        fontWeight: "400",
        fontStyle: "normal",
        textDecoration: "none",
        fontFamily: "Inter, sans-serif",
        color: "#333333",
        textAlign: "left",
        paddingTop: "0px",
        paddingBottom: "16px",
      },
      linkStyle: {
        type: "button",
        size: "medium",
      },
      imageStyle: {
        size: "medium",
        aspectRatio: "auto",
      },
      ...initialData,
    },
  });

  const watchedValues = watch();

  // Debug watched values
  console.log("👀 Current watched values:", {
    contentType: watchedValues.contentType,
    fileUrl: watchedValues.fileUrl,
    fileType: watchedValues.fileType,
    title: watchedValues.title,
  });

  useEffect(() => {
    if (initialData) {
      Object.keys(initialData).forEach((key) => {
        setValue(
          key as keyof HotspotContent,
          initialData[key as keyof HotspotContent]
        );
      });
    }
  }, [initialData, setValue]);

  useEffect(() => {
    if (uploadedImageUrl) {
      setValue("imageUrl", uploadedImageUrl);
    }
  }, [uploadedImageUrl, setValue]);

  const handleFileUpload = (url: string) => {
    // Ensure the URL includes the backend server URL
    const fullUrl = url.startsWith("http")
      ? url
      : `${process.env.NEXT_PUBLIC_API_URL}${url}`;
    setUploadedImageUrl(fullUrl);
    setValue("imageUrl", fullUrl);
  };

  const handleContentFileUpload = (result: {
    fileUrl: string;
    fileType: "image" | "pdf";
    metadata: any;
  }) => {
    console.log("📁 File upload result received:", result);

    const fullUrl = result.fileUrl.startsWith("http")
      ? result.fileUrl
      : `${process.env.NEXT_PUBLIC_API_URL}${result.fileUrl}`;

    console.log("🔗 Full file URL:", fullUrl);

    setFileContent({
      fileUrl: fullUrl,
      fileType: result.fileType,
      metadata: result.metadata,
    });

    setValue("contentType", "file");
    setValue("fileUrl", fullUrl);
    setValue("fileType", result.fileType);
    setValue("fileMetadata", result.metadata);

    // Clear standard content when switching to file mode
    setValue("title", result.metadata.originalName || "File Content");
    setValue("subtitle", "");
    setValue("description", "");
    setValue("imageUrl", "");

    // Force form to re-render by triggering validation
    setTimeout(() => {
      console.log("🔄 Triggering form re-render");
      // This will force the watched values to update
      setValue("contentType", "file", { shouldValidate: true });
    }, 100);

    console.log("✅ Form values updated for file content");
  };

  const handleClearFileContent = () => {
    setFileContent(undefined);
    setValue("contentType", "standard");
    setValue("fileUrl", "");
    setValue("fileType", undefined);
    setValue("fileMetadata", undefined);

    // Reset to standard content
    setValue("title", "");
    setValue("subtitle", "");
    setValue("description", "");
  };

  const handleFormSubmit = (data: HotspotContent) => {
    onSubmit(data);
  };

  const handleClose = () => {
    reset();
    setUploadedImageUrl("");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-7xl max-h-[95vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <div className="flex flex-col flex-1 overflow-hidden min-h-0">
          <div className="flex flex-col lg:flex-row gap-6 flex-1 overflow-hidden min-h-0">
            {/* Form Section - Left Side */}
            <div className="flex-1 overflow-y-auto pr-2 min-h-0">
              <div className="space-y-6">
                {/* Basic Content */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Content</h3>

                  {/* Language Selection */}
                  <div className="space-y-2">
                    <Label htmlFor="languageCode">Language</Label>
                    <Select
                      value={watchedValues.languageCode}
                      onValueChange={(value) => setValue("languageCode", value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {languages.map((lang) => (
                          <SelectItem key={lang.code} value={lang.code}>
                            {lang.nativeName} ({lang.name})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.languageCode && (
                      <p className="text-sm text-destructive">
                        {errors.languageCode.message}
                      </p>
                    )}
                  </div>

                  {/* Content Type Selection */}
                  <div className="space-y-2">
                    <Label>Content Type</Label>
                    <RadioGroup
                      value={watchedValues.contentType || "standard"}
                      onValueChange={(value: string) =>
                        setValue("contentType", value as "standard" | "file")
                      }
                      className="flex gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="standard" id="standard" />
                        <Label htmlFor="standard">Standard Content</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="file" id="file" />
                        <Label htmlFor="file">File Content (Image)</Label>
                        {/* TODO: Add "PDF/" back when PDF viewer is working */}
                      </div>
                    </RadioGroup>
                  </div>

                  {/* File Upload Section */}
                  {watchedValues.contentType === "file" && (
                    <div className="space-y-4">
                      <ContentFileUpload
                        onFileUpload={handleContentFileUpload}
                        onClear={handleClearFileContent}
                        currentFile={fileContent}
                      />
                    </div>
                  )}

                  {/* Standard Content Fields */}
                  {watchedValues.contentType !== "file" && (
                    <>
                      {/* Title */}
                      <div className="space-y-2">
                        <Label htmlFor="title">Title *</Label>
                        <Input
                          id="title"
                          placeholder="Enter hotspot title"
                          {...register("title")}
                        />
                        {errors.title && (
                          <p className="text-sm text-destructive">
                            {errors.title.message}
                          </p>
                        )}
                      </div>

                      {/* Subtitle */}
                      <div className="space-y-2">
                        <Label htmlFor="subtitle">Subtitle</Label>
                        <Input
                          id="subtitle"
                          placeholder="Enter subtitle (optional)"
                          {...register("subtitle")}
                        />
                      </div>

                      {/* Description */}
                      <div className="space-y-2">
                        <Label htmlFor="description">Description *</Label>
                        <RichTextEditor
                          content={watchedValues.description || ""}
                          onChange={(html, json) => {
                            setValue("description", html);
                            setValue("descriptionHtml", html);
                            setValue("descriptionJson", JSON.stringify(json));
                          }}
                          placeholder="Enter detailed description with rich formatting..."
                        />
                        {/* Hidden inputs to register rich text fields with React Hook Form */}
                        <input type="hidden" {...register("descriptionHtml")} />
                        <input type="hidden" {...register("descriptionJson")} />
                        {errors.description && (
                          <p className="text-sm text-destructive">
                            {errors.description.message}
                          </p>
                        )}
                      </div>
                    </>
                  )}
                </div>

                {watchedValues.contentType !== "file" && (
                  <>
                    <Separator />

                    {/* Image Upload/URL */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium flex items-center gap-2">
                        <ImageIcon className="h-5 w-5" />
                        Image
                      </h3>

                      <Tabs defaultValue="upload" className="w-full">
                        <TabsList className="grid w-full grid-cols-2">
                          <TabsTrigger value="upload">Upload Image</TabsTrigger>
                          <TabsTrigger value="url">Image URL</TabsTrigger>
                        </TabsList>

                        <TabsContent value="upload" className="space-y-2">
                          <FileUpload
                            onFileSelect={(file: File) => {
                              // File selected, upload will be handled automatically
                              console.log("File selected:", file.name);
                            }}
                            onFileUpload={handleFileUpload}
                            accept="image/*"
                            maxSize={10}
                            uploadEndpoint="/api/upload/hotspot-image"
                            preview={true}
                          />
                        </TabsContent>

                        <TabsContent value="url" className="space-y-2">
                          <Input
                            placeholder="https://example.com/image.jpg"
                            {...register("imageUrl")}
                          />
                          {errors.imageUrl && (
                            <p className="text-sm text-destructive">
                              {errors.imageUrl.message}
                            </p>
                          )}
                        </TabsContent>
                      </Tabs>

                      {/* Image Styling Controls */}
                      {(watchedValues.imageUrl || uploadedImageUrl) && (
                        <div className="space-y-4 mt-4 p-4 bg-muted/30 rounded-lg">
                          <h4 className="text-sm font-medium">
                            Image Display Settings
                          </h4>

                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label>Image Size</Label>
                              <Select
                                value={
                                  watchedValues.imageStyle?.size || "medium"
                                }
                                onValueChange={(value) =>
                                  setValue(
                                    "imageStyle.size",
                                    value as "small" | "medium" | "large"
                                  )
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="small">
                                    Small (60% width)
                                  </SelectItem>
                                  <SelectItem value="medium">
                                    Medium (80% width)
                                  </SelectItem>
                                  <SelectItem value="large">
                                    Large (100% width)
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="space-y-2">
                              <Label>Aspect Ratio</Label>
                              <Select
                                value={
                                  watchedValues.imageStyle?.aspectRatio ||
                                  "auto"
                                }
                                onValueChange={(value) =>
                                  setValue(
                                    "imageStyle.aspectRatio",
                                    value as "auto" | "16:9" | "4:3" | "1:1"
                                  )
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="auto">
                                    Auto (Original)
                                  </SelectItem>
                                  <SelectItem value="16:9">
                                    16:9 (Widescreen)
                                  </SelectItem>
                                  <SelectItem value="4:3">
                                    4:3 (Standard)
                                  </SelectItem>
                                  <SelectItem value="1:1">
                                    1:1 (Square)
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    <Separator />

                    {/* Link Configuration */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium flex items-center gap-2">
                        <LinkIcon className="h-5 w-5" />
                        Link
                      </h3>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="linkUrl">Link URL</Label>
                          <Input
                            id="linkUrl"
                            placeholder="https://example.com"
                            {...register("linkUrl")}
                          />
                          {errors.linkUrl && (
                            <p className="text-sm text-destructive">
                              {errors.linkUrl.message}
                            </p>
                          )}
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="linkText">Link Text</Label>
                          <Input
                            id="linkText"
                            placeholder="Learn More"
                            {...register("linkText")}
                          />
                        </div>
                      </div>

                      {/* Link Style */}
                      <div className="space-y-2">
                        <Label>Link Style</Label>
                        <RadioGroup
                          value={watchedValues.linkStyle?.type || "button"}
                          onValueChange={(value: string) =>
                            setValue(
                              "linkStyle.type",
                              value as "button" | "link"
                            )
                          }
                          className="flex gap-6"
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="button" id="button" />
                            <Label htmlFor="button">Button Style</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="link" id="link" />
                            <Label htmlFor="link">Underlined Link</Label>
                          </div>
                        </RadioGroup>
                      </div>
                    </div>
                  </>
                )}

                {watchedValues.contentType !== "file" && (
                  <>
                    <Separator />

                    {/* Typography & Styling */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium flex items-center gap-2">
                        <Type className="h-5 w-5" />
                        Typography & Styling
                      </h3>

                      <Tabs defaultValue="title" className="w-full">
                        <TabsList className="grid w-full grid-cols-3">
                          <TabsTrigger value="title">Title</TabsTrigger>
                          <TabsTrigger value="subtitle">Subtitle</TabsTrigger>
                          <TabsTrigger value="description">
                            Description
                          </TabsTrigger>
                        </TabsList>

                        {/* Title Styling */}
                        <TabsContent value="title" className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label>Font Family</Label>
                              <Select
                                value={
                                  watchedValues.titleStyle?.fontFamily ||
                                  "Inter, sans-serif"
                                }
                                onValueChange={(value) =>
                                  setValue("titleStyle.fontFamily", value)
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {fontFamilies.map((font) => (
                                    <SelectItem
                                      key={font.value}
                                      value={font.value}
                                    >
                                      {font.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label>Font Size</Label>
                              <Select
                                value={
                                  watchedValues.titleStyle?.fontSize || "18px"
                                }
                                onValueChange={(value) =>
                                  setValue("titleStyle.fontSize", value)
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {fontSizes.map((size) => (
                                    <SelectItem
                                      key={size.value}
                                      value={size.value}
                                    >
                                      {size.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label>Font Weight</Label>
                              <Select
                                value={
                                  watchedValues.titleStyle?.fontWeight || "600"
                                }
                                onValueChange={(value) =>
                                  setValue("titleStyle.fontWeight", value)
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {fontWeights.map((weight) => (
                                    <SelectItem
                                      key={weight.value}
                                      value={weight.value}
                                    >
                                      {weight.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label>Text Color</Label>
                              <div className="flex gap-2">
                                <Input
                                  type="color"
                                  value={
                                    watchedValues.titleStyle?.color || "#000000"
                                  }
                                  onChange={(e) =>
                                    setValue("titleStyle.color", e.target.value)
                                  }
                                  className="w-16 h-10"
                                />
                                <Input
                                  value={
                                    watchedValues.titleStyle?.color || "#000000"
                                  }
                                  onChange={(e) =>
                                    setValue("titleStyle.color", e.target.value)
                                  }
                                  placeholder="#000000"
                                />
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-4">
                            <Button
                              type="button"
                              variant={
                                watchedValues.titleStyle?.fontStyle === "italic"
                                  ? "default"
                                  : "outline"
                              }
                              size="sm"
                              onClick={() =>
                                setValue(
                                  "titleStyle.fontStyle",
                                  watchedValues.titleStyle?.fontStyle ===
                                    "italic"
                                    ? "normal"
                                    : "italic"
                                )
                              }
                            >
                              <Italic className="h-4 w-4" />
                            </Button>
                            <Button
                              type="button"
                              variant={
                                watchedValues.titleStyle?.fontWeight === "700"
                                  ? "default"
                                  : "outline"
                              }
                              size="sm"
                              onClick={() =>
                                setValue(
                                  "titleStyle.fontWeight",
                                  watchedValues.titleStyle?.fontWeight === "700"
                                    ? "400"
                                    : "700"
                                )
                              }
                            >
                              <Bold className="h-4 w-4" />
                            </Button>
                            <Button
                              type="button"
                              variant={
                                watchedValues.titleStyle?.textDecoration ===
                                "underline"
                                  ? "default"
                                  : "outline"
                              }
                              size="sm"
                              onClick={() =>
                                setValue(
                                  "titleStyle.textDecoration",
                                  watchedValues.titleStyle?.textDecoration ===
                                    "underline"
                                    ? "none"
                                    : "underline"
                                )
                              }
                            >
                              <Underline className="h-4 w-4" />
                            </Button>
                          </div>

                          {/* Text Alignment and Padding */}
                          <div className="grid grid-cols-3 gap-4">
                            <div className="space-y-2">
                              <Label>Text Alignment</Label>
                              <Select
                                value={
                                  watchedValues.titleStyle?.textAlign || "left"
                                }
                                onValueChange={(value) =>
                                  setValue(
                                    "titleStyle.textAlign",
                                    value as "left" | "center" | "right"
                                  )
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="left">Left</SelectItem>
                                  <SelectItem value="center">Center</SelectItem>
                                  <SelectItem value="right">Right</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label>Padding Top</Label>
                              <Select
                                value={
                                  watchedValues.titleStyle?.paddingTop || "0px"
                                }
                                onValueChange={(value) =>
                                  setValue("titleStyle.paddingTop", value)
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="0px">None</SelectItem>
                                  <SelectItem value="4px">
                                    Small (4px)
                                  </SelectItem>
                                  <SelectItem value="8px">
                                    Medium (8px)
                                  </SelectItem>
                                  <SelectItem value="12px">
                                    Large (12px)
                                  </SelectItem>
                                  <SelectItem value="16px">
                                    XL (16px)
                                  </SelectItem>
                                  <SelectItem value="24px">
                                    XXL (24px)
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label>Padding Bottom</Label>
                              <Select
                                value={
                                  watchedValues.titleStyle?.paddingBottom ||
                                  "8px"
                                }
                                onValueChange={(value) =>
                                  setValue("titleStyle.paddingBottom", value)
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="0px">None</SelectItem>
                                  <SelectItem value="4px">
                                    Small (4px)
                                  </SelectItem>
                                  <SelectItem value="8px">
                                    Medium (8px)
                                  </SelectItem>
                                  <SelectItem value="12px">
                                    Large (12px)
                                  </SelectItem>
                                  <SelectItem value="16px">
                                    XL (16px)
                                  </SelectItem>
                                  <SelectItem value="24px">
                                    XXL (24px)
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </TabsContent>

                        {/* Subtitle Styling */}
                        <TabsContent value="subtitle" className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label>Font Family</Label>
                              <Select
                                value={
                                  watchedValues.subtitleStyle?.fontFamily ||
                                  "Inter, sans-serif"
                                }
                                onValueChange={(value) =>
                                  setValue("subtitleStyle.fontFamily", value)
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {fontFamilies.map((font) => (
                                    <SelectItem
                                      key={font.value}
                                      value={font.value}
                                    >
                                      {font.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label>Font Size</Label>
                              <Select
                                value={
                                  watchedValues.subtitleStyle?.fontSize ||
                                  "14px"
                                }
                                onValueChange={(value) =>
                                  setValue("subtitleStyle.fontSize", value)
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {fontSizes.map((size) => (
                                    <SelectItem
                                      key={size.value}
                                      value={size.value}
                                    >
                                      {size.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label>Font Weight</Label>
                              <Select
                                value={
                                  watchedValues.subtitleStyle?.fontWeight ||
                                  "400"
                                }
                                onValueChange={(value) =>
                                  setValue("subtitleStyle.fontWeight", value)
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {fontWeights.map((weight) => (
                                    <SelectItem
                                      key={weight.value}
                                      value={weight.value}
                                    >
                                      {weight.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label>Text Color</Label>
                              <div className="flex gap-2">
                                <Input
                                  type="color"
                                  value={
                                    watchedValues.subtitleStyle?.color ||
                                    "#666666"
                                  }
                                  onChange={(e) =>
                                    setValue(
                                      "subtitleStyle.color",
                                      e.target.value
                                    )
                                  }
                                  className="w-16 h-10"
                                />
                                <Input
                                  value={
                                    watchedValues.subtitleStyle?.color ||
                                    "#666666"
                                  }
                                  onChange={(e) =>
                                    setValue(
                                      "subtitleStyle.color",
                                      e.target.value
                                    )
                                  }
                                  placeholder="#666666"
                                />
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-4">
                            <Button
                              type="button"
                              variant={
                                watchedValues.subtitleStyle?.fontStyle ===
                                "italic"
                                  ? "default"
                                  : "outline"
                              }
                              size="sm"
                              onClick={() =>
                                setValue(
                                  "subtitleStyle.fontStyle",
                                  watchedValues.subtitleStyle?.fontStyle ===
                                    "italic"
                                    ? "normal"
                                    : "italic"
                                )
                              }
                            >
                              <Italic className="h-4 w-4" />
                            </Button>
                            <Button
                              type="button"
                              variant={
                                watchedValues.subtitleStyle?.fontWeight ===
                                "700"
                                  ? "default"
                                  : "outline"
                              }
                              size="sm"
                              onClick={() =>
                                setValue(
                                  "subtitleStyle.fontWeight",
                                  watchedValues.subtitleStyle?.fontWeight ===
                                    "700"
                                    ? "400"
                                    : "700"
                                )
                              }
                            >
                              <Bold className="h-4 w-4" />
                            </Button>
                            <Button
                              type="button"
                              variant={
                                watchedValues.subtitleStyle?.textDecoration ===
                                "underline"
                                  ? "default"
                                  : "outline"
                              }
                              size="sm"
                              onClick={() =>
                                setValue(
                                  "subtitleStyle.textDecoration",
                                  watchedValues.subtitleStyle
                                    ?.textDecoration === "underline"
                                    ? "none"
                                    : "underline"
                                )
                              }
                            >
                              <Underline className="h-4 w-4" />
                            </Button>
                          </div>

                          {/* Text Alignment and Padding */}
                          <div className="grid grid-cols-3 gap-4">
                            <div className="space-y-2">
                              <Label>Text Alignment</Label>
                              <Select
                                value={
                                  watchedValues.subtitleStyle?.textAlign ||
                                  "left"
                                }
                                onValueChange={(value) =>
                                  setValue(
                                    "subtitleStyle.textAlign",
                                    value as "left" | "center" | "right"
                                  )
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="left">Left</SelectItem>
                                  <SelectItem value="center">Center</SelectItem>
                                  <SelectItem value="right">Right</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label>Padding Top</Label>
                              <Select
                                value={
                                  watchedValues.subtitleStyle?.paddingTop ||
                                  "0px"
                                }
                                onValueChange={(value) =>
                                  setValue("subtitleStyle.paddingTop", value)
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="0px">None</SelectItem>
                                  <SelectItem value="4px">
                                    Small (4px)
                                  </SelectItem>
                                  <SelectItem value="8px">
                                    Medium (8px)
                                  </SelectItem>
                                  <SelectItem value="12px">
                                    Large (12px)
                                  </SelectItem>
                                  <SelectItem value="16px">
                                    XL (16px)
                                  </SelectItem>
                                  <SelectItem value="24px">
                                    XXL (24px)
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label>Padding Bottom</Label>
                              <Select
                                value={
                                  watchedValues.subtitleStyle?.paddingBottom ||
                                  "8px"
                                }
                                onValueChange={(value) =>
                                  setValue("subtitleStyle.paddingBottom", value)
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="0px">None</SelectItem>
                                  <SelectItem value="4px">
                                    Small (4px)
                                  </SelectItem>
                                  <SelectItem value="8px">
                                    Medium (8px)
                                  </SelectItem>
                                  <SelectItem value="12px">
                                    Large (12px)
                                  </SelectItem>
                                  <SelectItem value="16px">
                                    XL (16px)
                                  </SelectItem>
                                  <SelectItem value="24px">
                                    XXL (24px)
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </TabsContent>

                        {/* Description Styling */}
                        <TabsContent value="description" className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label>Font Family</Label>
                              <Select
                                value={
                                  watchedValues.descriptionStyle?.fontFamily ||
                                  "Inter, sans-serif"
                                }
                                onValueChange={(value) =>
                                  setValue("descriptionStyle.fontFamily", value)
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {fontFamilies.map((font) => (
                                    <SelectItem
                                      key={font.value}
                                      value={font.value}
                                    >
                                      {font.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label>Font Size</Label>
                              <Select
                                value={
                                  watchedValues.descriptionStyle?.fontSize ||
                                  "16px"
                                }
                                onValueChange={(value) =>
                                  setValue("descriptionStyle.fontSize", value)
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {fontSizes.map((size) => (
                                    <SelectItem
                                      key={size.value}
                                      value={size.value}
                                    >
                                      {size.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label>Font Weight</Label>
                              <Select
                                value={
                                  watchedValues.descriptionStyle?.fontWeight ||
                                  "400"
                                }
                                onValueChange={(value) =>
                                  setValue("descriptionStyle.fontWeight", value)
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {fontWeights.map((weight) => (
                                    <SelectItem
                                      key={weight.value}
                                      value={weight.value}
                                    >
                                      {weight.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label>Text Color</Label>
                              <div className="flex gap-2">
                                <Input
                                  type="color"
                                  value={
                                    watchedValues.descriptionStyle?.color ||
                                    "#333333"
                                  }
                                  onChange={(e) =>
                                    setValue(
                                      "descriptionStyle.color",
                                      e.target.value
                                    )
                                  }
                                  className="w-16 h-10"
                                />
                                <Input
                                  value={
                                    watchedValues.descriptionStyle?.color ||
                                    "#333333"
                                  }
                                  onChange={(e) =>
                                    setValue(
                                      "descriptionStyle.color",
                                      e.target.value
                                    )
                                  }
                                  placeholder="#333333"
                                />
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-4">
                            <Button
                              type="button"
                              variant={
                                watchedValues.descriptionStyle?.fontStyle ===
                                "italic"
                                  ? "default"
                                  : "outline"
                              }
                              size="sm"
                              onClick={() =>
                                setValue(
                                  "descriptionStyle.fontStyle",
                                  watchedValues.descriptionStyle?.fontStyle ===
                                    "italic"
                                    ? "normal"
                                    : "italic"
                                )
                              }
                            >
                              <Italic className="h-4 w-4" />
                            </Button>
                            <Button
                              type="button"
                              variant={
                                watchedValues.descriptionStyle?.fontWeight ===
                                "700"
                                  ? "default"
                                  : "outline"
                              }
                              size="sm"
                              onClick={() =>
                                setValue(
                                  "descriptionStyle.fontWeight",
                                  watchedValues.descriptionStyle?.fontWeight ===
                                    "700"
                                    ? "400"
                                    : "700"
                                )
                              }
                            >
                              <Bold className="h-4 w-4" />
                            </Button>
                            <Button
                              type="button"
                              variant={
                                watchedValues.descriptionStyle
                                  ?.textDecoration === "underline"
                                  ? "default"
                                  : "outline"
                              }
                              size="sm"
                              onClick={() =>
                                setValue(
                                  "descriptionStyle.textDecoration",
                                  watchedValues.descriptionStyle
                                    ?.textDecoration === "underline"
                                    ? "none"
                                    : "underline"
                                )
                              }
                            >
                              <Underline className="h-4 w-4" />
                            </Button>
                          </div>

                          {/* Text Alignment and Padding */}
                          <div className="grid grid-cols-3 gap-4">
                            <div className="space-y-2">
                              <Label>Text Alignment</Label>
                              <Select
                                value={
                                  watchedValues.descriptionStyle?.textAlign ||
                                  "left"
                                }
                                onValueChange={(value) =>
                                  setValue(
                                    "descriptionStyle.textAlign",
                                    value as "left" | "center" | "right"
                                  )
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="left">Left</SelectItem>
                                  <SelectItem value="center">Center</SelectItem>
                                  <SelectItem value="right">Right</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label>Padding Top</Label>
                              <Select
                                value={
                                  watchedValues.descriptionStyle?.paddingTop ||
                                  "0px"
                                }
                                onValueChange={(value) =>
                                  setValue("descriptionStyle.paddingTop", value)
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="0px">None</SelectItem>
                                  <SelectItem value="4px">
                                    Small (4px)
                                  </SelectItem>
                                  <SelectItem value="8px">
                                    Medium (8px)
                                  </SelectItem>
                                  <SelectItem value="12px">
                                    Large (12px)
                                  </SelectItem>
                                  <SelectItem value="16px">
                                    XL (16px)
                                  </SelectItem>
                                  <SelectItem value="24px">
                                    XXL (24px)
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label>Padding Bottom</Label>
                              <Select
                                value={
                                  watchedValues.descriptionStyle
                                    ?.paddingBottom || "16px"
                                }
                                onValueChange={(value) =>
                                  setValue(
                                    "descriptionStyle.paddingBottom",
                                    value
                                  )
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="0px">None</SelectItem>
                                  <SelectItem value="4px">
                                    Small (4px)
                                  </SelectItem>
                                  <SelectItem value="8px">
                                    Medium (8px)
                                  </SelectItem>
                                  <SelectItem value="12px">
                                    Large (12px)
                                  </SelectItem>
                                  <SelectItem value="16px">
                                    XL (16px)
                                  </SelectItem>
                                  <SelectItem value="24px">
                                    XXL (24px)
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </TabsContent>
                      </Tabs>

                      {/* Link Size Control */}
                      <div className="space-y-2">
                        <Label>Link/Button Size</Label>
                        <Select
                          value={watchedValues.linkStyle?.size || "medium"}
                          onValueChange={(value) =>
                            setValue(
                              "linkStyle.size",
                              value as "small" | "medium" | "large"
                            )
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="small">Small</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="large">Large</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Preview Section - Right Side */}
            <div className="lg:w-96 border-l pl-6 overflow-y-auto min-h-0">
              <h3 className="text-lg font-medium mb-4 sticky top-0 bg-background z-10 pb-2">
                Live Preview
              </h3>

              {/* Dynamic Hotspot Dialog Preview */}
              <DynamicHotspotDialog
                content={{
                  title: watchedValues.title || "Sample Title",
                  subtitle: watchedValues.subtitle || "",
                  description:
                    watchedValues.description || "Enter content to see preview",
                  imageUrl: watchedValues.imageUrl || uploadedImageUrl || "",
                  linkUrl: watchedValues.linkUrl || "",
                  linkText: watchedValues.linkText || "",
                  languageCode: watchedValues.languageCode || "en",
                  contentType: watchedValues.contentType || "standard",
                  fileUrl: watchedValues.fileUrl || "",
                  fileType: watchedValues.fileType,
                  fileMetadata: watchedValues.fileMetadata,
                  titleStyle: watchedValues.titleStyle,
                  subtitleStyle: watchedValues.subtitleStyle,
                  descriptionStyle: watchedValues.descriptionStyle,
                  linkStyle: watchedValues.linkStyle,
                  imageStyle: watchedValues.imageStyle,
                }}
                isPreview={true}
                showCloseButton={false}
                className="w-full"
              />
            </div>
          </div>

          <DialogFooter className="flex-shrink-0 mt-4">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button
              type="button"
              disabled={isSubmitting}
              onClick={async () => {
                console.log("🖱️ Save Content button clicked");

                // Get current form values manually (like panorama form)
                const watchedValues = watch();
                console.log("🔍 All watched values:", watchedValues);

                const formData = {
                  title: watchedValues.title || "",
                  subtitle: watchedValues.subtitle || "",
                  description: watchedValues.description || "",
                  imageUrl: watchedValues.imageUrl || uploadedImageUrl || "",
                  linkUrl: watchedValues.linkUrl || "",
                  linkText: watchedValues.linkText || "",
                  languageCode: watchedValues.languageCode || "en",
                  contentType: watchedValues.contentType || "standard",
                  fileUrl: watchedValues.fileUrl || "",
                  fileType: watchedValues.fileType,
                  fileMetadata: watchedValues.fileMetadata,
                  titleStyle: watchedValues.titleStyle || {
                    fontSize: "18px",
                    fontWeight: "600",
                    fontStyle: "normal",
                    textDecoration: "none",
                    fontFamily: "Inter, sans-serif",
                    color: "#000000",
                    textAlign: "left",
                    paddingTop: "0px",
                    paddingBottom: "8px",
                  },
                  subtitleStyle: watchedValues.subtitleStyle || {
                    fontSize: "14px",
                    fontWeight: "400",
                    fontStyle: "normal",
                    textDecoration: "none",
                    fontFamily: "Inter, sans-serif",
                    color: "#666666",
                    textAlign: "left",
                    paddingTop: "0px",
                    paddingBottom: "8px",
                  },
                  descriptionStyle: watchedValues.descriptionStyle || {
                    fontSize: "16px",
                    fontWeight: "400",
                    fontStyle: "normal",
                    textDecoration: "none",
                    fontFamily: "Inter, sans-serif",
                    color: "#333333",
                    textAlign: "left",
                    paddingTop: "0px",
                    paddingBottom: "16px",
                  },
                  linkStyle: watchedValues.linkStyle || {
                    type: "button",
                    size: "medium",
                  },
                  imageStyle: watchedValues.imageStyle || {
                    size: "medium",
                    aspectRatio: "auto",
                  },
                };

                console.log("📋 Form data collected:", formData);
                onSubmit(formData as any);
              }}
            >
              {isSubmitting ? "Saving..." : "Save Content"}
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
};
