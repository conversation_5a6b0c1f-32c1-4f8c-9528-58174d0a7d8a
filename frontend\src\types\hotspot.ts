interface HotspotContentStyle {
  fontSize?: string;
  fontWeight?: string;
  fontStyle?: string;
  textDecoration?: string;
  fontFamily?: string;
  color?: string;
  textAlign?: "left" | "center" | "right";
  paddingTop?: string;
  paddingBottom?: string;
}

export interface HotspotContent {
  id?: string;
  title: string;
  subtitle?: string;
  description: string;
  // Rich text fields for TipTap editor
  descriptionJson?: any; // TipTap JSON format
  descriptionHtml?: string; // Rendered HTML
  imageUrl?: string;
  linkUrl?: string;
  linkText?: string;
  languageCode: string;
  order?: number; // Order for slideshow sequencing
  // Styling fields
  titleStyle?: HotspotContentStyle;
  subtitleStyle?: HotspotContentStyle;
  descriptionStyle?: HotspotContentStyle;
  linkStyle?: {
    type: "button" | "link";
    size?: "small" | "medium" | "large";
  };
  imageStyle?: {
    size: "small" | "medium" | "large";
    aspectRatio: "auto" | "16:9" | "4:3" | "1:1";
  };
  // File content properties
  contentType?: "standard" | "file";
  fileUrl?: string;
  fileType?: "image" | "pdf";
  fileMetadata?: {
    originalName?: string;
    originalUrl?: string;
    size?: number;
    mimeType?: string;
  };
}

export interface Hotspot {
  id: string;
  xPosition: number;
  yPosition: number;
  iconType: string;
  iconColor: string;
  panoramaId: string;
  content?: HotspotContent[];
  isTemporary?: boolean;
}

export interface Panorama {
  id: string;
  title: string;
  description?: string;
  imageUrl: string;
  isActive: boolean;
}
