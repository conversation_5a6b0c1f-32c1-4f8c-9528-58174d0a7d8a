# Production Environment Variables
NODE_ENV=production

# Database
DATABASE_URL=postgresql://root:Onamission%23007@37.27.84.251:5432/iduna_db

# API Configuration
BACKEND_PORT=5000
FRONTEND_PORT=3000
API_BASE_URL=http://37.27.84.251:5000
FRONTEND_BASE_URL=http://37.27.84.251:3000

# Authentication
JWT_SECRET=CHANGE_THIS_IN_PRODUCTION_SUPER_SECRET_KEY_2024
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_EXPIRES_IN=30d

# Redis
REDIS_URL=redis://redis:6379

# File Upload
MAX_FILE_SIZE=50MB
UPLOAD_PATH=/app/uploads

# CORS
CORS_ORIGIN=http://37.27.84.251:3000

# WordPress Integration
WORDPRESS_INTEGRATION_URL=https://your-wordpress-site.com

# Logging
LOG_LEVEL=info

# SSL
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem
