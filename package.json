{"name": "iduview", "version": "1.0.0", "description": "360° panorama viewer with interactive hotspots", "scripts": {"dev": "docker-compose -f docker-compose.dev.yml up --build", "dev:down": "docker-compose -f docker-compose.dev.yml down", "dev:logs": "docker-compose -f docker-compose.dev.yml logs -f", "dev:local": "node dev-local.js", "dev:start": "node start-dev.js", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "docker-compose build", "up": "docker-compose up -d", "down": "docker-compose down", "logs": "docker-compose logs -f", "db:migrate": "cd backend && npm run db:migrate", "db:migrate:prod": "cd backend && npm run db:migrate:prod", "db:seed": "cd backend && npm run db:seed", "db:generate": "cd backend && npm run db:generate", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "clean": "docker-compose down -v && docker system prune -f", "setup": "npm run install:all && npm run db:generate && npm run db:migrate && npm run db:seed", "setup:db": "node setup-db.js"}, "keywords": ["360", "panorama", "hotspots", "viewer", "docker", "nextjs", "express"], "author": "Iduna.dk", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}