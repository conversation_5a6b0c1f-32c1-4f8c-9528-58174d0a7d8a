#!/bin/bash

# IduView Deployment Script
# This script builds and deploys the IduView application using Docker

set -e  # Exit on any error

echo "🚀 Starting IduView Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    print_warning ".env.production file not found. Creating from template..."
    if [ -f ".env.example" ]; then
        cp .env.example .env.production
        print_warning "Please edit .env.production with your production values before running again."
        exit 1
    else
        print_error "No .env.example file found. Please create .env.production manually."
        exit 1
    fi
fi

print_status "Environment file found: .env.production"

# Stop existing containers
print_status "Stopping existing containers..."
docker-compose down --remove-orphans || true

# Remove old images (optional - uncomment if you want to force rebuild)
# print_status "Removing old images..."
# docker-compose down --rmi all --volumes --remove-orphans || true

# Build and start containers
print_status "Building and starting containers..."
docker-compose up --build -d

# Wait for services to be ready
print_status "Waiting for services to start..."
sleep 10

# Check if containers are running
print_status "Checking container status..."
if docker-compose ps | grep -q "Up"; then
    print_success "Containers are running!"
    
    # Show running containers
    echo ""
    print_status "Running containers:"
    docker-compose ps
    
    echo ""
    print_success "🎉 Deployment completed successfully!"
    print_status "Frontend: http://localhost:3000"
    print_status "Backend API: http://localhost:5000"
    print_status "Redis: localhost:6380"
    print_warning "Configure your global Nginx using the iduview-nginx.conf file"
    
    echo ""
    print_status "To view logs: docker-compose logs -f"
    print_status "To stop: docker-compose down"
    print_status "To restart: docker-compose restart"
    
else
    print_error "Some containers failed to start!"
    print_status "Checking logs..."
    docker-compose logs --tail=50
    exit 1
fi

# Optional: Run database migrations
read -p "Do you want to run database migrations? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Running database migrations..."
    docker-compose exec backend npx prisma migrate deploy
    print_success "Database migrations completed!"
fi

# Optional: Seed database
read -p "Do you want to seed the database with initial data? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Seeding database..."
    docker-compose exec backend npm run seed
    print_success "Database seeding completed!"
fi

print_success "🚀 IduView is now running!"
