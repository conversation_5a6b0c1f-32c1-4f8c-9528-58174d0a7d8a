#!/bin/bash

# Test Docker builds
set -e

echo "🔧 Testing Docker builds..."

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test backend build
print_status "Building backend container..."
if docker build -t iduview-backend-test ./backend; then
    print_success "Backend build successful!"
else
    print_error "Backend build failed!"
    exit 1
fi

# Test frontend build
print_status "Building frontend container..."
if docker build -t iduview-frontend-test ./frontend; then
    print_success "Frontend build successful!"
else
    print_error "Frontend build failed!"
    exit 1
fi

print_success "🎉 All builds completed successfully!"

# Clean up test images
print_status "Cleaning up test images..."
docker rmi iduview-backend-test iduview-frontend-test || true

print_success "✅ Build test completed!"
