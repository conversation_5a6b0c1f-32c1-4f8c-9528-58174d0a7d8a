# IduView - 360° Panorama Viewer

A comprehensive 360° panorama viewer with interactive hotspots, admin dashboard, and multi-language support. Built for WordPress integration and containerized deployment.

## 🚀 Features

- **360° Panorama Viewer**: Smooth navigation with Pannellum
- **Interactive Hotspots**: Clickable markers with rich content modals
- **Admin Dashboard**: Complete content management system
- **Multi-language Support**: Dynamic language switching with admin management
- **WordPress Integration**: Easy embedding with locale inheritance
- **Containerized**: Docker-ready for production deployment
- **Responsive Design**: Works on all devices

## 🏗️ Architecture

- **Frontend**: Next.js 14 with TypeScript, Tailwind CSS
- **Backend**: Express.js with TypeScript, Prisma ORM
- **Database**: PostgreSQL
- **Cache**: Redis (production)
- **Deployment**: Docker + Docker Compose + Nginx

## 📋 Prerequisites

- Docker and Docker Compose
- Node.js 18+ (for local development)
- PostgreSQL database

## 🛠️ Installation

### 1. <PERSON><PERSON> and Setup

```bash
git clone <repository-url>
cd iduview
npm run install:all
```

### 2. Environment Configuration

Copy and configure environment files:

```bash
cp .env.example .env.development
cp .env.example .env.production
```

Edit the environment files with your configuration:
- Database connection string
- JWT secrets
- API URLs
- Redis configuration (production)

### 3. Database Setup

```bash
# Generate Prisma client
npm run db:generate

# Run migrations
npm run db:migrate

# Seed with sample data
npm run db:seed
```

### 4. Add Your Panorama Image

Place your `3d_Kitchen_06.jpg` file in:
```
frontend/public/panoramas/3d_Kitchen_06.jpg
```

## 🚀 Development

### Start Development Environment

```bash
# Start all services with Docker
npm run dev

# Or start services individually
npm run dev:backend
npm run dev:frontend
```

### Access Points

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **Admin Dashboard**: http://localhost:3000/admin

### Default Admin Credentials

- **Email**: <EMAIL>
- **Password**: admin123

## 🐳 Production Deployment

### 1. Configure Production Environment

Edit `.env.production` with your production settings:

```env
NODE_ENV=production
DATABASE_URL=postgresql://user:password@host:port/database
API_BASE_URL=https://your-domain.com/api
FRONTEND_BASE_URL=https://your-domain.com
JWT_SECRET=your-super-secret-jwt-key
REDIS_URL=redis://redis:6379
```

### 2. Deploy with Docker

```bash
# Build and start production containers
npm run build
npm run up

# View logs
npm run logs

# Stop services
npm run down
```

### 3. Nginx Configuration

The included nginx configuration provides:
- Reverse proxy for frontend and backend
- Static file serving
- Rate limiting
- Security headers
- SSL termination (configure certificates)

## 📱 WordPress Integration

### Option 1: Iframe Embed

```php
// WordPress shortcode
function iduview_panorama_shortcode($atts) {
    $locale = get_locale();
    $panorama_id = $atts['id'] ?? '1';
    $url = "https://your-domain.com/viewer/{$panorama_id}?lang={$locale}";
    return "<iframe src='{$url}' width='100%' height='600px' frameborder='0'></iframe>";
}
add_shortcode('iduview_panorama', 'iduview_panorama_shortcode');

// Usage: [iduview_panorama id="1"]
```

### Option 2: Direct Integration

```javascript
// Embeddable widget
<script src="https://your-domain.com/embed.js"></script>
<div id="iduview-container" data-panorama="1" data-lang="auto"></div>
```

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

### Panoramas
- `GET /api/panoramas` - List panoramas
- `GET /api/panoramas/:id` - Get panorama details
- `POST /api/panoramas` - Create panorama (admin)
- `PUT /api/panoramas/:id` - Update panorama (admin)
- `DELETE /api/panoramas/:id` - Delete panorama (admin)

### Hotspots
- `GET /api/hotspots/panorama/:id` - Get hotspots for panorama
- `POST /api/hotspots` - Create hotspot (admin)
- `PUT /api/hotspots/:id` - Update hotspot (admin)
- `DELETE /api/hotspots/:id` - Delete hotspot (admin)

### Languages
- `GET /api/languages` - List languages
- `POST /api/languages` - Create language (admin)
- `PUT /api/languages/:code` - Update language (admin)
- `DELETE /api/languages/:code` - Delete language (admin)

### File Upload
- `POST /api/upload/panorama` - Upload panorama image (admin)
- `POST /api/upload/hotspot-image` - Upload hotspot image (admin)

## 🎨 Customization

### Styling

The project uses Tailwind CSS for styling. Customize the theme in:
```
frontend/tailwind.config.js
```

### Hotspot Icons

Customize hotspot appearance in:
```
frontend/src/components/PanoramaViewer.tsx
```

### Languages

Add new languages through the admin dashboard or directly in the database.

## 🔍 Monitoring

### Health Checks

- **Backend**: http://localhost:5000/health
- **Frontend**: http://localhost:3000/api/health

### Logs

```bash
# View all logs
npm run logs

# View specific service logs
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f nginx
```

## 🛡️ Security

- JWT-based authentication
- Rate limiting on API endpoints
- Input validation and sanitization
- CORS configuration
- Security headers via Nginx
- File upload restrictions

## 📊 Performance

- Redis caching (production)
- Image optimization with Sharp
- Gzip compression
- Static file caching
- CDN-ready static assets

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check DATABASE_URL in environment files
   - Ensure PostgreSQL is accessible

2. **Redis Connection Failed**
   - Redis is only used in production
   - Check REDIS_URL configuration

3. **File Upload Issues**
   - Check file size limits
   - Verify upload directory permissions

4. **CORS Errors**
   - Check CORS_ORIGIN in backend configuration
   - Verify API_BASE_URL in frontend

### Reset Everything

```bash
# Stop all containers and remove volumes
npm run clean

# Reinstall dependencies
npm run install:all

# Reset database
npm run db:migrate
npm run db:seed
```

## 📝 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For support and questions, please contact: <EMAIL>
