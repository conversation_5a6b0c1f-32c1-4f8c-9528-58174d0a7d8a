import { useEffect, useState } from "react";
import { useLanguageStore, AppLanguage } from "@/store/languageStore";

interface TranslationData {
  [key: string]: any;
}

export const useAppTranslation = () => {
  const { language } = useLanguageStore();
  const [translations, setTranslations] = useState<TranslationData>({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadTranslations = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`/locales/${language}/common.json`);
        if (response.ok) {
          const data = await response.json();
          setTranslations(data);
        } else {
          console.warn(`Failed to load translations for ${language}`);
          // Fallback to English
          const fallbackResponse = await fetch("/locales/en/common.json");
          if (fallbackResponse.ok) {
            const fallbackData = await fallbackResponse.json();
            setTranslations(fallbackData);
          }
        }
      } catch (error) {
        console.error("Error loading translations:", error);
        // Set empty object as fallback
        setTranslations({});
      } finally {
        setIsLoading(false);
      }
    };

    loadTranslations();
  }, [language]);

  const t = (
    key: string,
    fallback?: string,
    interpolation?: Record<string, any>
  ): string => {
    const keys = key.split(".");
    let value = translations;

    for (const k of keys) {
      if (value && typeof value === "object" && k in value) {
        value = value[k];
      } else {
        return fallback || key;
      }
    }

    let result = typeof value === "string" ? value : fallback || key;

    // Simple interpolation support
    if (interpolation) {
      Object.entries(interpolation).forEach(([placeholder, replacement]) => {
        const regex = new RegExp(`{${placeholder}}`, "g");
        result = result.replace(regex, String(replacement));
      });
    }

    return result;
  };

  return { t, isLoading, language };
};
