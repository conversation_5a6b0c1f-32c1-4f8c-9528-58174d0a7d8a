import multer from "multer";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import { config } from "../utils/config";

// Convert size string to bytes
const parseSize = (size: string): number => {
  const units: { [key: string]: number } = {
    B: 1,
    KB: 1024,
    MB: 1024 * 1024,
    GB: 1024 * 1024 * 1024,
  };

  const match = size.match(/^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB)$/i);
  if (!match) return 50 * 1024 * 1024; // Default 50MB

  const value = parseFloat(match[1]);
  const unit = match[2].toUpperCase();

  return value * (units[unit] || 1);
};

// Storage configuration
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(__dirname, "..", "..", "uploads");
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = uuidv4();
    const extension = path.extname(file.originalname);
    const filename = `${uniqueSuffix}${extension}`;
    cb(null, filename);
  },
});

// File filter for panorama images
const panoramaFileFilter = (
  req: any,
  file: Express.Multer.File,
  cb: multer.FileFilterCallback
) => {
  const allowedMimes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];

  if (allowedMimes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(
      new Error(
        "Invalid file type. Only JPEG, PNG, and WebP images are allowed."
      )
    );
  }
};

// File filter for hotspot images
const hotspotImageFilter = (
  req: any,
  file: Express.Multer.File,
  cb: multer.FileFilterCallback
) => {
  const allowedMimes = [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/webp",
    "image/gif",
  ];

  if (allowedMimes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(
      new Error(
        "Invalid file type. Only JPEG, PNG, WebP, and GIF images are allowed."
      )
    );
  }
};

// File filter for content files (images and PDFs)
const contentFileFilter = (
  req: any,
  file: Express.Multer.File,
  cb: multer.FileFilterCallback
) => {
  const allowedMimes = [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/webp",
    "image/gif",
    "application/pdf",
  ];

  if (allowedMimes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(
      new Error(
        "Invalid file type. Only JPEG, PNG, WebP, GIF images and PDF files are allowed."
      )
    );
  }
};

// Multer configurations
export const uploadPanorama = multer({
  storage,
  fileFilter: panoramaFileFilter,
  limits: {
    fileSize: parseSize(config.maxFileSize),
    files: 1,
  },
});

export const uploadHotspotImage = multer({
  storage,
  fileFilter: hotspotImageFilter,
  limits: {
    fileSize: parseSize("10MB"), // Smaller limit for hotspot images
    files: 1,
  },
});

export const uploadContentFile = multer({
  storage,
  fileFilter: contentFileFilter,
  limits: {
    fileSize: parseSize("50MB"), // Larger limit for content files (PDFs can be bigger)
    files: 1,
  },
});

// Generic upload for other files
export const uploadGeneric = multer({
  storage,
  limits: {
    fileSize: parseSize(config.maxFileSize),
    files: 5,
  },
});
