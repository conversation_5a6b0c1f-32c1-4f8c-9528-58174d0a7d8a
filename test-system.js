#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🧪 Testing IduView System...\n');

const testBackend = () => {
  return new Promise((resolve, reject) => {
    console.log('📡 Testing backend compilation...');
    
    const backend = spawn('npm', ['run', 'build'], {
      cwd: path.join(__dirname, 'backend'),
      stdio: 'pipe',
      shell: true,
      env: { 
        ...process.env,
        NODE_ENV: 'development',
        DATABASE_URL: 'postgresql://root:Onamission%23007@************:5432/iduna_db',
        JWT_SECRET: 'dev_jwt_secret_change_in_production_2024'
      }
    });

    let output = '';
    let errorOutput = '';

    backend.stdout.on('data', (data) => {
      output += data.toString();
    });

    backend.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    backend.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Backend compilation successful!');
        resolve();
      } else {
        console.log('❌ Backend compilation failed:');
        console.log(errorOutput);
        reject(new Error('Backend compilation failed'));
      }
    });
  });
};

const testFrontend = () => {
  return new Promise((resolve, reject) => {
    console.log('🌐 Testing frontend compilation...');
    
    const frontend = spawn('npm', ['run', 'build'], {
      cwd: path.join(__dirname, 'frontend'),
      stdio: 'pipe',
      shell: true,
      env: { 
        ...process.env,
        NODE_ENV: 'production',
        NEXT_PUBLIC_API_URL: 'http://localhost:5000',
        NEXT_PUBLIC_FRONTEND_URL: 'http://localhost:3000'
      }
    });

    let output = '';
    let errorOutput = '';

    frontend.stdout.on('data', (data) => {
      output += data.toString();
    });

    frontend.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    frontend.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Frontend compilation successful!');
        resolve();
      } else {
        console.log('❌ Frontend compilation failed:');
        console.log(errorOutput);
        reject(new Error('Frontend compilation failed'));
      }
    });
  });
};

async function runTests() {
  try {
    console.log('🔧 Installing dependencies...\n');
    
    // Install backend dependencies
    await new Promise((resolve, reject) => {
      const backendInstall = spawn('npm', ['install'], {
        cwd: path.join(__dirname, 'backend'),
        stdio: 'inherit',
        shell: true
      });
      
      backendInstall.on('close', (code) => {
        if (code === 0) resolve();
        else reject(new Error('Backend install failed'));
      });
    });

    // Install frontend dependencies
    await new Promise((resolve, reject) => {
      const frontendInstall = spawn('npm', ['install'], {
        cwd: path.join(__dirname, 'frontend'),
        stdio: 'inherit',
        shell: true
      });
      
      frontendInstall.on('close', (code) => {
        if (code === 0) resolve();
        else reject(new Error('Frontend install failed'));
      });
    });

    console.log('\n🧪 Running compilation tests...\n');
    
    // Test backend compilation
    await testBackend();
    
    // Test frontend compilation
    await testFrontend();
    
    console.log('\n🎉 All tests passed!');
    console.log('\n🚀 System is ready to start:');
    console.log('   npm run dev:start');
    console.log('\n🎯 Features available:');
    console.log('   ✅ 360° Panorama Viewer');
    console.log('   ✅ Interactive Hotspots');
    console.log('   ✅ Beautiful Popup System');
    console.log('   ✅ Admin Authentication');
    console.log('   ✅ Language Management');
    console.log('   ✅ Complete Dashboard');
    
  } catch (error) {
    console.error('\n❌ Tests failed:', error.message);
    process.exit(1);
  }
}

runTests();
