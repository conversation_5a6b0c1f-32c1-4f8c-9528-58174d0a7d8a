# Example Environment Variables
# Copy this file to .env.development or .env.production and fill in your values

NODE_ENV=development

# Database
DATABASE_URL=postgresql://username:password@host:port/database

# API Configuration
BACKEND_PORT=5000
FRONTEND_PORT=3000
API_BASE_URL=http://localhost:5000
FRONTEND_BASE_URL=http://localhost:3000

# Authentication
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_EXPIRES_IN=30d

# Redis
REDIS_URL=redis://localhost:6379

# File Upload
MAX_FILE_SIZE=50MB
UPLOAD_PATH=./uploads

# CORS
CORS_ORIGIN=http://localhost:3000

# WordPress Integration
WORDPRESS_INTEGRATION_URL=http://localhost:8080

# Logging
LOG_LEVEL=debug
