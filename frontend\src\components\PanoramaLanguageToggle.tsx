// frontend/src/components/PanoramaLanguageToggle.tsx

import React, { useState, useEffect } from "react";
import { Globe } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import axios from "axios";

interface Language {
  code: string;
  name: string;
  nativeName: string;
  isActive: boolean;
  isDefault: boolean;
}

interface PanoramaLanguageToggleProps {
  selectedLanguage: string;
  onLanguageChange: (languageCode: string) => void;
  variant?: "default" | "ghost" | "outline";
  size?: "default" | "sm" | "lg" | "icon";
  showLabel?: boolean;
  className?: string;
}

export const PanoramaLanguageToggle: React.FC<PanoramaLanguageToggleProps> = ({
  selectedLanguage,
  onLanguageChange,
  variant = "ghost",
  size = "sm",
  showLabel = true,
  className = "",
}) => {
  const [languages, setLanguages] = useState<Language[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchActiveLanguages();
  }, []);

  const fetchActiveLanguages = async () => {
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_API_URL}/api/languages`
      );

      // Filter only active languages
      const activeLanguages = response.data.data.languages.filter(
        (lang: Language) => lang.isActive
      );

      setLanguages(activeLanguages);

      // If no language is selected, set to default language
      if (!selectedLanguage && activeLanguages.length > 0) {
        const defaultLang =
          activeLanguages.find((lang: Language) => lang.isDefault) ||
          activeLanguages[0];
        onLanguageChange(defaultLang.code);
      }
    } catch (error) {
      console.error("Failed to fetch languages:", error);
    } finally {
      setLoading(false);
    }
  };

  const getCurrentLanguage = () => {
    return (
      languages.find((lang) => lang.code === selectedLanguage) || languages[0]
    );
  };

  const handleLanguageChange = (languageCode: string) => {
    onLanguageChange(languageCode);
  };

  // Don't render if no languages or only one language
  if (loading || languages.length <= 1) {
    return null;
  }

  const currentLang = getCurrentLanguage();

  return (
    <div className="flex items-center space-x-2">
      {/* Content Language Label */}
      {/* <Badge variant="secondary" className="text-xs">
        Content
      </Badge> */}

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant={variant}
            size={size}
            className={`${className} min-w-0`}
          >
            <Globe className="h-4 w-4" />
            {showLabel && currentLang && (
              <span className="ml-2 text-sm">{currentLang.nativeName}</span>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <div className="px-2 py-1 text-xs text-muted-foreground border-b mb-1">
            Hotspot Content Language
          </div>
          {languages.map((lang) => (
            <DropdownMenuItem
              key={lang.code}
              onClick={() => handleLanguageChange(lang.code)}
              className={`cursor-pointer ${
                selectedLanguage === lang.code ? "bg-accent" : ""
              }`}
            >
              <div className="flex flex-col w-full">
                <div className="flex items-center justify-between">
                  <span className="font-medium">{lang.name}</span>
                  {selectedLanguage === lang.code && (
                    <Badge variant="default" className="text-xs ml-2">
                      Active
                    </Badge>
                  )}
                </div>
                <span className="text-xs text-muted-foreground">
                  {lang.nativeName}
                </span>
              </div>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
