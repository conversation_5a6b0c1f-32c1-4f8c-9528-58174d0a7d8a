{"title": "IduView - 360° Panorama Viewer", "description": "Interactive 360° panorama viewer with hotspots", "loading": "Loading...", "error": "An error occurred", "close": "Close", "learnMore": "Learn More", "viewSpecifications": "View Specifications", "downloadApp": "Download App", "login": {"title": "<PERSON><PERSON>", "subtitle": "360° Panorama Management System", "description": "Sign in to your account to continue", "email": "Email", "password": "Password", "emailPlaceholder": "<EMAIL>", "passwordPlaceholder": "Enter your password", "loginButton": "Sign In", "loggingIn": "Signing in...", "success": "Login successful!", "failed": "<PERSON><PERSON> failed", "demoCredentials": "Demo Credentials", "demoEmailLabel": "Email:", "demoPasswordLabel": "Password:", "footer": "Secure admin access for panorama management", "validation": {"emailInvalid": "Invalid email", "emailRequired": "Email is required", "passwordMinLength": "Password must be at least 6 characters", "passwordRequired": "Password is required"}}, "navigation": {"adminAccess": "Admin Access", "adminLogin": "<PERSON><PERSON>"}, "homepage": {"noPanoramas": "No Panoramas Available", "noPanoramasDescription": "No panoramas have been added yet. You need to log in as an admin to add panoramas and hotspots.", "loading": {"title": "IduView - Loading...", "panorama": "Loading panorama...", "viewer": "Loading 360° viewer...", "failed": "Failed to load panorama data"}, "meta": {"description": "Interactive 360° panorama with hotspots"}, "badge": {"experience": "Interactive 360° Experience", "hotspot": "Hotspot", "hotspots": "Hotspots"}, "navigation": {"title": "How to Navigate", "description": "Use these controls to explore the 360° environment", "clickHotspots": "Click hotspots for more information", "dragToLook": "Drag to look around", "scrollToZoom": "Scroll to zoom"}, "features": {"interactiveHotspots": "Interactive Hotspots", "interactiveHotspotsDesc": "Discover detailed information by clicking on the {{count}} interactive hotspot{{plural}} in this panorama.", "interactiveHotspotsDescEmpty": "Interactive hotspots can be added to provide detailed information about specific areas or features.", "interactiveNavigation": "Interactive Navigation", "interactiveNavigationDesc": "Click and drag to explore the 360° environment with smooth navigation controls. Drag to look around and scroll to zoom.", "immersiveViewing": "Immersive Viewing", "immersiveViewingDesc": "Experience high-quality panoramic imagery with interactive elements that bring the environment to life."}}, "admin": {"dashboard": "Dashboard", "panoramas": "Panoramas", "hotspots": "Hotspots", "languages": "Languages", "settings": "Settings", "adminPanel": "Admin Panel", "logout": "Logout", "loggedOutSuccess": "Logged out successfully", "dashboardPage": {"loading": "Loading dashboard...", "loadError": "Failed to load dashboard data", "welcome": "Welcome back!", "subtitle": "Here's what's happening with your 360° panorama system today.", "stats": {"totalPanoramas": "Total Panoramas", "totalHotspots": "Total Hotspots", "languages": "Languages", "viewsToday": "Views Today", "fromLastMonth": "from last month", "active": "active", "today": "Today", "changePositive12": "+12%", "changePositive8": "+8%"}, "quickActions": {"title": "Quick Actions", "subtitle": "Get started with common tasks", "uploadPanorama": "Upload Panorama", "uploadPanoramaDesc": "Add a new 360° panorama", "addHotspot": "Add Hotspot", "addHotspotDesc": "Create interactive hotspots", "manageLanguages": "Manage Languages", "manageLanguagesDesc": "Configure language settings"}, "recentActivity": {"title": "Recent Activity", "subtitle": "Latest updates and changes", "noActivity": "No recent activity", "noActivityDesc": "Activity will appear here as you use the system."}}, "panoramasPage": {"loading": "Loading panoramas...", "title": "Panoramas", "subtitle": "Manage your 360° panorama collection", "addPanorama": "Add Panorama", "form": {"editTitle": "Edit Panorama", "addTitle": "Add Panorama", "editDescription": "Update the panorama details below.", "addDescription": "Add a new 360° panorama to your collection.", "titleLabel": "Title", "titlePlaceholder": "e.g., Modern Kitchen 360°", "descriptionLabel": "Description", "descriptionPlaceholder": "Describe this panorama...", "imageLabel": "Panorama Image", "uploadFile": "Upload File", "imageUrl": "Image URL", "urlPlaceholder": "/panoramas/kitchen.jpg or https://example.com/image.jpg", "cancel": "Cancel", "saving": "Saving...", "update": "Update", "create": "Create", "validation": {"titleRequired": "Title is required", "urlInvalid": "Must be a valid URL", "imageRequired": "Please upload a file or provide an image URL"}}, "stats": {"totalPanoramas": "Total Panoramas", "active": "active", "activePanoramas": "Active Panoramas", "availableForViewing": "Available for viewing", "totalHotspots": "Total Hotspots", "interactivePoints": "Interactive points"}, "table": {"title": "Panorama Management", "subtitle": "Configure and manage your 360° panorama collection", "noPanoramas": "No panoramas", "noPanoramasDesc": "Get started by adding your first 360° panorama.", "headers": {"panorama": "Panorama", "hotspots": "Hotspots", "status": "Status", "created": "Created", "active": "Active", "actions": "Actions"}, "noDescription": "No description", "statusActive": "Active", "statusInactive": "Inactive"}, "actions": {"viewPanorama": "View Panorama", "previewEmbed": "Preview Embed", "getEmbedCode": "Get Embed Code", "visualEditor": "Visual Hotspot Editor", "editPanorama": "Edit Panorama", "deletePanorama": "Delete Panorama"}, "messages": {"fetchFailed": "Failed to fetch panoramas", "uploadSuccess": "File uploaded successfully!", "updateSuccess": "Panorama updated successfully", "createSuccess": "Panorama created successfully", "deleteConfirm": "Are you sure you want to delete this panorama?", "deleteSuccess": "Panorama deleted successfully", "activated": "activated", "deactivated": "deactivated", "copySuccess": "Copied to clipboard!", "copyFailed": "Failed to copy to clipboard", "uploadWarning": "Uploading a new picture will result in a loss of the hotspots of this panorama picture! Are you sure?", "imageUpdateNotSupported": "Image updates are not yet supported. Please create a new panorama instead."}, "embedDialog": {"title": "Embed Code for {title}", "description": "Copy the iframe code below to embed this panorama on your website or WordPress.", "preview": "Preview", "directUrls": "Direct URLs", "iframeCode": "Iframe Embed Code", "wordpressInstructions": "WordPress Instructions", "wordpressTitle": "📝 How to embed in WordPress:", "wordpressSteps": {"step1": "Copy the iframe code above", "step2": "In WordPress editor, switch to \"Text\" or \"HTML\" mode", "step3": "Paste the iframe code where you want the panorama", "step4": "Switch back to \"Visual\" mode and publish"}, "close": "Close"}}, "hotspotsPage": {"loading": "Loading hotspots...", "title": "Hotspots & Content", "subtitle": "Manage interactive hotspots and their content for panoramas", "addHotspot": "Add Hotspot", "visualEditor": "Visual Editor", "hotspotForm": {"editTitle": "Edit Hotspot", "addTitle": "Add Hotspot", "editDescription": "Update the hotspot details below.", "addDescription": "Add a new interactive hotspot to a panorama.", "panoramaLabel": "Panorama", "panoramaPlaceholder": "Select a panorama", "xPositionLabel": "X Position (%)", "yPositionLabel": "Y Position (%)", "xPositionPlaceholder": "25.5", "yPositionPlaceholder": "45.2", "iconTypeLabel": "Icon Type", "iconTypePlaceholder": "Select icon type", "iconColorLabel": "Icon Color", "iconColorPlaceholder": "Select icon color", "cancel": "Cancel", "saving": "Saving...", "update": "Update", "create": "Create", "validation": {"xPositionRequired": "X position is required", "yPositionRequired": "Y position is required", "iconTypeRequired": "Icon type is required", "iconColorRequired": "Icon color is required", "panoramaRequired": "Panorama is required"}}, "iconTypes": {"info": "Info", "warning": "Warning", "success": "Success", "error": "Error"}, "iconColors": {"white": "White", "blue": "Blue", "green": "Green", "red": "Red", "yellow": "Yellow", "purple": "Purple"}, "stats": {"totalHotspots": "Total Hotspots", "interactivePoints": "Interactive points", "panoramasWithHotspots": "Panoramas with Hotspots", "outOfTotal": "Out of {count} total", "totalContent": "Total Content", "contentItems": "Content items"}, "management": {"title": "Hotspot & Content Management", "subtitle": "Configure hotspots and manage their content across languages", "noHotspots": "No hotspots", "noPanoramasMessage": "Add a panorama first, then create hotspots for it.", "noHotspotsMessage": "Get started by adding your first interactive hotspot.", "position": "Position: {x}%, {y}%", "contentCount": "Content ({count} items)", "addContent": "Add Content", "noContentMessage": "No content added yet. Click \"Add Content\" to get started.", "linkLabel": "Link: {text}"}, "contentForm": {"editTitle": "Edit Content", "addTitle": "Add Content", "editDescription": "Update the content details below.", "addDescription": "Add content for hotspot in {panorama}.", "languageLabel": "Language", "languagePlaceholder": "Select language", "titleLabel": "Title *", "titlePlaceholder": "e.g., <PERSON><PERSON>na Kitchen Cleaner", "subtitleLabel": "Subtitle", "subtitlePlaceholder": "e.g., Premium Multi-Surface Formula", "descriptionLabel": "Description *", "descriptionPlaceholder": "Describe the product or feature...", "imageUrlLabel": "Image URL", "imageUrlPlaceholder": "https://example.com/image.jpg", "linkUrlLabel": "Link URL", "linkUrlPlaceholder": "https://iduna.dk/product", "linkTextLabel": "Link Text", "linkTextPlaceholder": "e.g., Shop at Iduna.dk", "cancel": "Cancel", "saving": "Saving...", "update": "Update", "create": "Create", "validation": {"titleRequired": "Title is required", "descriptionRequired": "Description is required", "imageUrlInvalid": "Must be a valid URL", "linkUrlInvalid": "Must be a valid URL", "languageRequired": "Language is required"}}, "messages": {"fetchFailed": "Failed to fetch data", "hotspotUpdateSuccess": "Hotspot updated successfully", "hotspotCreateSuccess": "Hotspot created successfully", "contentUpdateSuccess": "Content updated successfully", "contentCreateSuccess": "Content created successfully", "hotspotDeleteConfirm": "Are you sure you want to delete this hotspot?", "contentDeleteConfirm": "Are you sure you want to delete this content?", "hotspotDeleteSuccess": "Hotspot deleted successfully", "contentDeleteSuccess": "Content deleted successfully"}}, "languagesPage": {"loading": "Loading languages...", "title": "Languages", "subtitle": "Manage language locales for hotspot content", "addLanguage": "Add Language", "form": {"editTitle": "Edit Language", "addTitle": "Add Language", "editDescription": "Update the language settings below.", "addDescription": "Add a new language locale for your content.", "codeLabel": "Language Code", "codePlaceholder": "e.g., en, da, de-DE", "nameLabel": "Name", "namePlaceholder": "e.g., English", "nativeNameLabel": "Native Name", "nativeNamePlaceholder": "e.g., English", "defaultLabel": "Set as default language", "cancel": "Cancel", "saving": "Saving...", "update": "Update", "create": "Create", "validation": {"codeInvalid": "Invalid language code format", "nameRequired": "Name is required", "nativeNameRequired": "Native name is required"}}, "stats": {"totalLanguages": "Total Languages", "active": "active", "activeLanguages": "Active Languages", "availableForContent": "Available for content", "totalContent": "Total Content", "hotspotContentItems": "Hotspot content items"}, "table": {"title": "Language Management", "subtitle": "Configure and manage language settings for your content", "headers": {"language": "Language", "code": "Code", "content": "Content", "status": "Status", "active": "Active", "actions": "Actions"}, "contentItems": "items", "statusActive": "Active", "statusInactive": "Inactive", "defaultBadge": "<PERSON><PERSON><PERSON>"}, "messages": {"fetchFailed": "Failed to fetch languages", "updateSuccess": "Language updated successfully", "createSuccess": "Language created successfully", "operationFailed": "Operation failed", "deleteConfirm": "Are you sure you want to delete this language?", "deleteSuccess": "Language deleted successfully", "deleteFailed": "Delete failed", "activated": "activated", "deactivated": "deactivated", "updateFailed": "Update failed"}}, "settingsPage": {"title": "Settings", "subtitle": "Configure your IduView system preferences and options", "tabs": {"general": "General", "user": "User", "system": "System", "display": "Display"}, "general": {"title": "General Settings", "subtitle": "Basic configuration for your IduView installation", "siteName": "Site Name", "defaultLanguage": "Default Language", "siteDescription": "Site Description", "languages": {"english": "English", "danish": "Danish", "german": "German"}}, "user": {"title": "User Preferences", "subtitle": "Personal settings and notification preferences", "emailNotifications": {"label": "Email Notifications", "description": "Receive email notifications for important updates"}, "pushNotifications": {"label": "Push Notifications", "description": "Receive browser push notifications"}, "autoSave": {"label": "Auto Save", "description": "Automatically save changes as you work"}}, "system": {"title": "System Configuration", "subtitle": "Advanced system settings and performance options", "maxFileSize": "<PERSON> (MB)", "viewerHeight": "Default Viewer Height (px)", "cacheEnabled": {"label": "<PERSON><PERSON>", "description": "Enable caching for better performance"}, "debugMode": {"label": "Debug Mode", "description": "Enable debug logging and error details"}}, "display": {"title": "Display & Appearance", "subtitle": "Customize the visual appearance of your system", "theme": "Theme", "hotspotColor": "Default Hotspot Color", "themes": {"light": "Light", "dark": "Dark", "auto": "Auto"}}, "systemInfo": {"title": "System Information", "version": "Version", "environment": "Environment", "database": "Database", "lastUpdated": "Last Updated", "development": "Development"}, "actions": {"resetToDefault": "Reset to De<PERSON>ult", "saveChanges": "Save Changes"}, "messages": {"saveSuccess": "{section} settings saved successfully", "saveFailed": "Failed to save settings", "resetConfirm": "Are you sure you want to reset {section} settings to default?", "resetSuccess": "{section} settings reset to default"}}, "visualEditorPage": {"selectPanoramaInstruction": "Select a panorama to start editing hotspots visually", "panoramaSelection": {"title": "Select Panorama", "description": "Choose a panorama to place and edit hotspots", "label": "Panorama:", "placeholder": "Select a panorama", "noPanoramas": "No panoramas available. Please create a panorama first."}}, "visualHotspotEditor": {"title": "Visual Hotspot Editor", "hideExisting": "Hide Existing", "showExisting": "Show Existing", "placeHotspot": "Place Hotspot", "cancelPlacing": "Cancel Placing", "placingModeActive": "Placing Mode Active", "viewMode": "View Mode", "existingHotspot": "{count} existing hotspot", "existingHotspots": "{count} existing hotspots", "position": "Position: {x}%, {y}%", "confirm": "Confirm", "cancel": "Cancel", "clickToPlace": "Click anywhere on the panorama to place a hotspot", "clickToPlaceOverlay": "Click to place hotspot", "instructions": {"title": "Instructions:", "placeMode": "Click \"Place Hotspot\" to enter placing mode", "clickPanorama": "Click anywhere on the panorama to position the hotspot", "confirmPosition": "Confirm the position or cancel to try again", "editExisting": "Click existing hotspots to edit their content", "toggleVisibility": "Use \"Show/Hide Existing\" to toggle hotspot visibility"}}, "panoramaViewer": {"controls": {"drag": "🖱️ Drag to look around", "click": "📍 Click hotspots for info", "zoom": "🔍 Scroll to zoom"}, "hotspotDialog": {"close": "Close", "defaultLinkText": "Shop at Iduna.dk"}}, "embedPage": {"loading": "Loading panorama...", "errors": {"notFound": "Panorama not found", "loadFailed": "Failed to load panorama", "notAvailable": "The panorama you're looking for doesn't exist or is not available."}, "meta": {"titleSuffix": "Embedded View", "description": "Embedded 360° view of {title}"}}, "panoramaPage": {"loading": "Loading panorama...", "errors": {"notActive": "This panorama is not currently active", "notFound": "Panorama not found", "loadFailed": "Failed to load panorama", "notAvailable": "The panorama you're looking for doesn't exist or is not available."}, "navigation": {"goHome": "Go Home", "backToGallery": "← Back to Gallery"}, "meta": {"titleSuffix": "IduView 360°", "defaultDescription": "Experience {title} in immersive 360° view"}, "footer": {"poweredBy": "Powered by IduView - Interactive 360° Panorama Platform"}}}, "toast": {"success": "Success", "error": "Error", "warning": "Warning", "info": "Info"}}