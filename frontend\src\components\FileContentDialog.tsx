import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { HotspotContent } from "@/types/hotspot";

interface FileContentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  content: HotspotContent;
}

export default function FileContentDialog({
  isOpen,
  onClose,
  content,
}: FileContentDialogProps) {
  if (content.contentType !== "file" || !content.fileUrl) {
    return null;
  }

  const fileUrl = content.fileUrl.startsWith("http")
    ? content.fileUrl
    : `${process.env.NEXT_PUBLIC_API_URL}${content.fileUrl}`;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[95vw] max-h-[95vh] w-fit h-fit p-0 overflow-hidden">
        <DialogHeader className="sr-only">
          <DialogTitle>{content.title}</DialogTitle>
          <DialogDescription>
            File content dialog displaying{" "}
            {content.fileType === "pdf" ? "PDF document" : "image file"}
          </DialogDescription>
        </DialogHeader>

        {content.fileType === "pdf" ? (
          <iframe
            src={`${fileUrl}#toolbar=0&navpanes=0&scrollbar=0`}
            className="w-[90vw] h-[80vh] md:w-[80vw] md:h-[85vh] lg:w-[70vw] lg:h-[90vh] border-0 rounded-lg"
            title={content.title}
          />
        ) : (
          <img
            src={fileUrl}
            alt={content.title}
            className="max-w-[95vw] max-h-[95vh] w-auto h-auto object-contain rounded-lg shadow-lg"
            style={{ imageRendering: "auto" }}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}
