{"name": "iduview-frontend", "version": "1.0.0", "description": "Frontend for 360° panorama viewer with hotspots", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@egjs/react-view360": "^4.0.0-beta.7", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@types/three": "^0.158.3", "axios": "^1.6.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "i18next": "^23.7.6", "js-cookie": "^3.0.5", "lucide-react": "^0.511.0", "next": "^15.3.2", "next-i18next": "^15.2.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.4.1", "react-i18next": "^13.5.0", "react-query": "^3.39.3", "react-toastify": "^9.1.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "three": "^0.158.0", "yup": "^1.3.3", "zod": "^3.25.30", "zustand": "^5.0.5"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/js-cookie": "^3.0.6", "@types/node": "^20.9.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "autoprefixer": "^10.4.16", "css-loader": "^6.8.1", "eslint": "^8.57.0", "eslint-config-next": "^15.0.3", "postcss": "^8.4.31", "style-loader": "^3.3.4", "tailwindcss": "^3.3.5", "typescript": "^5.2.2"}, "keywords": ["360", "panorama", "hotspots", "viewer", "nextjs", "react"], "author": "Iduna.dk", "license": "MIT"}