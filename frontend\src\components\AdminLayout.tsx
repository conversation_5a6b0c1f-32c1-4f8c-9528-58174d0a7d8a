import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import Link from "next/link";
import { toast } from "react-hot-toast";
import {
  Home,
  Image,
  MapPin,
  Languages,
  Menu,
  User,
  LogOut,
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
} from "@/components/ui/sheet";
import { LanguageToggle } from "@/components/LanguageToggle";
import { useAppTranslation } from "@/hooks/useAppTranslation";

interface AdminLayoutProps {
  children: React.ReactNode;
  title?: string;
}

interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role: string;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({
  children,
  title = "Admin Dashboard",
}) => {
  const { t } = useAppTranslation();
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  useEffect(() => {
    // Check authentication
    const token = localStorage.getItem("token");
    const userData = localStorage.getItem("user");

    if (!token || !userData) {
      router.push("/login");
      return;
    }

    try {
      setUser(JSON.parse(userData));
    } catch (error) {
      console.error("Error parsing user data:", error);
      router.push("/login");
    }
  }, [router]);

  const handleLogout = () => {
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    toast.success(t("admin.loggedOutSuccess", "Logged out successfully"));
    router.push("/login");
  };

  const navigation = [
    {
      name: t("admin.dashboard", "Dashboard"),
      href: "/admin",
      icon: Home,
      badge: null,
    },
    {
      name: t("admin.panoramas", "Panoramas"),
      href: "/admin/panoramas",
      icon: Image,
      badge: null,
    },
    {
      name: t("admin.hotspots", "Hotspots"),
      href: "/admin/hotspots",
      icon: MapPin,
      badge: null,
    },
    {
      name: t("admin.languages", "Languages"),
      href: "/admin/languages",
      icon: Languages,
      badge: "3",
    },
    // {
    //   name: t("admin.settings", "Settings"),
    //   href: "/admin/settings",
    //   icon: Settings,
    //   badge: null,
    // },
  ];

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <p className="text-sm text-muted-foreground">
            Loading admin panel...
          </p>
        </div>
      </div>
    );
  }

  // Sidebar component
  const SidebarContent = ({ mobile = false }: { mobile?: boolean }) => (
    <div className="flex h-full flex-col">
      {/* Logo */}
      <div className="flex h-16 items-center border-b px-6">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <span className="text-primary-foreground font-bold text-lg">I</span>
          </div>
          <div>
            <h1 className="text-xl font-bold tracking-tight">IduView</h1>
            <p className="text-xs text-muted-foreground">
              {t("admin.adminPanel", "Admin Panel")}
            </p>
          </div>
        </div>
        {!mobile && (
          <div className="ml-auto">
            <LanguageToggle size="sm" />
          </div>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 p-4">
        {navigation.map((item) => {
          const isActive = router.pathname === item.href;
          return (
            <Link key={item.name} href={item.href}>
              <Button
                variant={isActive ? "secondary" : "ghost"}
                className="w-full justify-start"
                size="default"
              >
                <item.icon className="h-4 w-4 mr-2" />
                {item.name}
                {item.badge && (
                  <Badge variant="secondary" className="ml-auto">
                    {item.badge}
                  </Badge>
                )}
              </Button>
            </Link>
          );
        })}
      </nav>

      {/* User section */}
      <div className="border-t p-4">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="w-full justify-start p-2">
              <Avatar className="h-8 w-8">
                <AvatarImage src="" />
                <AvatarFallback>
                  {user.firstName?.[0] || user.email[0].toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="ml-2 text-left">
                <p className="text-sm font-medium">
                  {user.firstName
                    ? `${user.firstName} ${user.lastName || ""}`
                    : user.email}
                </p>
                <p className="text-xs text-muted-foreground capitalize">
                  {user.role}
                </p>
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuItem onClick={handleLogout}>
              <LogOut className="mr-2 h-4 w-4" />
              {t("admin.logout", "Sign out")}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile sidebar */}
      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
        <SheetContent side="left" className="p-0 w-72">
          <SheetHeader className="sr-only">
            <SheetTitle>{t("admin.adminPanel", "Admin Panel")}</SheetTitle>
            <SheetDescription>
              Navigation menu for the admin panel
            </SheetDescription>
          </SheetHeader>
          <SidebarContent mobile />
        </SheetContent>
      </Sheet>

      <div className="flex">
        {/* Desktop sidebar - fixed height of 100vh */}
        <div className="hidden lg:block lg:w-72 border-r bg-card fixed left-0 top-0 h-[100vh] z-30 overflow-y-auto">
          <SidebarContent />
        </div>

        {/* Main content area - with left margin to account for fixed sidebar */}
        <div className="flex-1 lg:ml-72">
          {/* Header - show on mobile and tablet for menu button and user actions */}
          <header className="sticky top-0 z-40 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 lg:hidden">
            <div className="flex h-16 items-center gap-4 px-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarOpen(true)}
              >
                <Menu className="h-5 w-5" />
              </Button>

              <div className="flex-1">
                <h1 className="text-lg font-semibold">{title}</h1>
              </div>

              {/* Header actions */}
              <div className="flex items-center gap-2">
                <LanguageToggle size="sm" />
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src="" />
                        <AvatarFallback>
                          {user.firstName?.[0] || user.email[0].toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <DropdownMenuItem onClick={handleLogout}>
                      <LogOut className="mr-2 h-4 w-4" />
                      {t("admin.logout", "Sign out")}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </header>

          {/* Page content */}
          <main className="min-h-screen p-4 lg:p-6">
            <div className="mx-auto max-w-7xl">{children}</div>
          </main>
        </div>
      </div>
    </div>
  );
};

export default AdminLayout;
