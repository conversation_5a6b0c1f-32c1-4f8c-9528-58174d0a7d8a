import dotenv from "dotenv";
import path from "path";

// Load environment variables based on NODE_ENV
const envFile =
  process.env.NODE_ENV === "production"
    ? ".env.production"
    : ".env.development";

// Try to load from parent directory first, then current directory
const envPaths = [
  path.resolve(process.cwd(), "..", envFile), // When running from backend/
  path.resolve(process.cwd(), envFile), // When running from root/
];

let envLoaded = false;
for (const envPath of envPaths) {
  try {
    if (require("fs").existsSync(envPath)) {
      dotenv.config({ path: envPath });
      envLoaded = true;
      console.log(`📄 Loaded environment from: ${envPath}`);
      break;
    }
  } catch (error) {
    // Continue to next path
  }
}

if (!envLoaded) {
  console.warn("⚠️  No environment file found, using process.env variables");
}

export const config = {
  // Environment
  nodeEnv: process.env.NODE_ENV || "development",

  // Server
  port: parseInt(process.env.BACKEND_PORT || "5000", 10),

  // Database
  databaseUrl: process.env.DATABASE_URL!,

  // URLs
  apiBaseUrl: process.env.API_BASE_URL!,
  frontendBaseUrl: process.env.FRONTEND_BASE_URL!,

  // Authentication
  jwtSecret: process.env.JWT_SECRET!,
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || "7d",
  refreshTokenExpiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN || "30d",

  // Redis
  redisUrl: process.env.REDIS_URL || "redis://localhost:6379",
  useRedis: process.env.NODE_ENV === "production",

  // File Upload
  maxFileSize: process.env.MAX_FILE_SIZE || "50MB",
  uploadPath: process.env.UPLOAD_PATH || "./uploads",

  // CORS
  corsOrigin: process.env.CORS_ORIGIN || "http://localhost:3000",

  // WordPress
  wordpressIntegrationUrl: process.env.WORDPRESS_INTEGRATION_URL,

  // Logging
  logLevel: process.env.LOG_LEVEL || "info",

  // SSL (for production)
  sslCertPath: process.env.SSL_CERT_PATH,
  sslKeyPath: process.env.SSL_KEY_PATH,
};

// Validate required environment variables
const requiredEnvVars = [
  "DATABASE_URL",
  "JWT_SECRET",
  "API_BASE_URL",
  "FRONTEND_BASE_URL",
];

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
}

export default config;
