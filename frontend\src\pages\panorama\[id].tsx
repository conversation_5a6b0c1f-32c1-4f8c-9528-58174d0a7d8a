// frontend\src\pages\panorama\[id].tsx

import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import axios from "axios";
import dynamic from "next/dynamic";
import { toast } from "react-hot-toast";
import { Panorama, Hotspot } from "@/types/hotspot";
import { useAppTranslation } from "@/hooks/useAppTranslation";
import { PanoramaLanguageToggle } from "@/components/PanoramaLanguageToggle";
import { usePanoramaLanguageStore } from "@/store/panoramaLanguageStore";

// Dynamically import View360PanoramaViewer to avoid SSR issues
const View360PanoramaViewer = dynamic(
  () => import("@/components/View360PanoramaViewer"),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center h-full bg-muted">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Loading 360° viewer...</p>
        </div>
      </div>
    ),
  }
);

const PanoramaViewPage: React.FC = () => {
  const { t } = useAppTranslation();
  const router = useRouter();
  const { id, embed } = router.query;
  const [panorama, setPanorama] = useState<Panorama | null>(null);
  const [hotspots, setHotspots] = useState<Hotspot[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Panorama content language state
  const { contentLanguage, setContentLanguage } = usePanoramaLanguageStore();

  const isEmbedMode = embed === "true";

  useEffect(() => {
    if (id) {
      fetchPanoramaData();
    }
  }, [id, contentLanguage]);

  const fetchPanoramaData = async () => {
    try {
      setLoading(true);

      // Fetch panorama details
      const panoramaResponse = await axios.get(
        `${process.env.NEXT_PUBLIC_API_URL}/api/panoramas/${id}`
      );

      const panoramaData = panoramaResponse.data.data.panorama;

      if (!panoramaData.isActive && !isEmbedMode) {
        setError(
          t(
            "panoramaPage.errors.notActive",
            "This panorama is not currently active"
          )
        );
        return;
      }

      setPanorama(panoramaData);

      // Fetch hotspots for this panorama with selected language
      const hotspotsResponse = await axios.get(
        `${process.env.NEXT_PUBLIC_API_URL}/api/hotspots/panorama/${id}?lang=${contentLanguage}`
      );

      const hotspotsData = hotspotsResponse.data.data.hotspots || [];
      console.log("🎯 Fetched hotspots data:", hotspotsData);

      // Log styling data for debugging
      hotspotsData.forEach((hotspot: any, index: number) => {
        if (hotspot.content && hotspot.content[0]) {
          console.log(`🎨 Hotspot ${index + 1} styling:`, {
            titleStyle: hotspot.content[0].titleStyle,
            subtitleStyle: hotspot.content[0].subtitleStyle,
            descriptionStyle: hotspot.content[0].descriptionStyle,
            linkStyle: hotspot.content[0].linkStyle,
          });
        }
      });

      setHotspots(hotspotsData);
    } catch (error: any) {
      console.error("Error fetching panorama:", error);
      if (error.response?.status === 404) {
        setError(t("panoramaPage.errors.notFound", "Panorama not found"));
      } else {
        setError(
          t("panoramaPage.errors.loadFailed", "Failed to load panorama")
        );
      }
    } finally {
      setLoading(false);
    }
  };

  const handleHotspotClick = (hotspot: Hotspot) => {
    console.log("Hotspot clicked:", hotspot);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <p className="text-sm text-muted-foreground">
            {t("panoramaPage.loading", "Loading panorama...")}
          </p>
        </div>
      </div>
    );
  }

  if (error || !panorama) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <h1 className="text-2xl font-bold text-destructive">
            {error || t("panoramaPage.errors.notFound", "Panorama not found")}
          </h1>
          <p className="text-muted-foreground">
            {t(
              "panoramaPage.errors.notAvailable",
              "The panorama you're looking for doesn't exist or is not available."
            )}
          </p>
          {!isEmbedMode && (
            <button
              onClick={() => router.push("/")}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
            >
              {t("panoramaPage.navigation.goHome", "Go Home")}
            </button>
          )}
        </div>
      </div>
    );
  }

  const pageTitle = `${panorama.title} - ${t(
    "panoramaPage.meta.titleSuffix",
    "IduView 360°"
  )}`;
  const pageDescription =
    panorama.description ||
    t(
      "panoramaPage.meta.defaultDescription",
      "Experience {title} in immersive 360° view",
      { title: panorama.title }
    );

  return (
    <>
      <Head>
        <title>{pageTitle}</title>
        <meta name="description" content={pageDescription} />
        <meta property="og:title" content={pageTitle} />
        <meta property="og:description" content={pageDescription} />
        <meta property="og:type" content="website" />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover, interactive-widget=resizes-content"
        />

        {/* Iframe-specific meta tags */}
        {isEmbedMode && (
          <>
            <meta name="robots" content="noindex, nofollow" />
            <style jsx global>{`
              body {
                margin: 0;
                padding: 0;
                overflow: hidden;
              }
            `}</style>
          </>
        )}
      </Head>

      <div className={isEmbedMode ? "h-screen w-full" : "min-h-screen"}>
        {/* Panorama Language Toggle - Upper Right Corner */}
        <div className="absolute top-4 right-4 z-20">
          <PanoramaLanguageToggle
            selectedLanguage={contentLanguage}
            onLanguageChange={setContentLanguage}
            variant="outline"
            size="sm"
            showLabel={true}
          />
        </div>

        {!isEmbedMode && (
          <header className="bg-background border-b">
            <div className="container mx-auto px-4 py-4">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-2xl font-bold">{panorama.title}</h1>
                  {panorama.description && (
                    <p className="text-muted-foreground">
                      {panorama.description}
                    </p>
                  )}
                </div>
                <button
                  onClick={() => router.push("/")}
                  className="px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/80"
                >
                  {t(
                    "panoramaPage.navigation.backToGallery",
                    "← Back to Gallery"
                  )}
                </button>
              </div>
            </div>
          </header>
        )}

        <main className={isEmbedMode ? "h-full" : "flex-1"}>
          <View360PanoramaViewer
            panoramaUrl={`${process.env.NEXT_PUBLIC_API_URL}${panorama.imageUrl}`}
            hotspots={hotspots}
            onHotspotClick={handleHotspotClick}
            className={isEmbedMode ? "h-full" : ""}
            height={isEmbedMode ? "100vh" : "90vh"}
          />
        </main>

        {!isEmbedMode && (
          <footer className="bg-background border-t py-4">
            <div className="container mx-auto px-4 text-center text-sm text-muted-foreground">
              <p>
                {t(
                  "panoramaPage.footer.poweredBy",
                  "Powered by IduView - Interactive 360° Panorama Platform"
                )}
              </p>
            </div>
          </footer>
        )}
      </div>
    </>
  );
};

export default PanoramaViewPage;
