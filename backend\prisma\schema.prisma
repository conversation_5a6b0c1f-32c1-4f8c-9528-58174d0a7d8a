// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Panorama {
  id            String         @id @default(cuid())
  filename      String         @unique
  title         String
  description   String?
  width         Int
  height        Int
  fileSize      Int
  mimeType      String
  thumbnailPath String?        // Path to thumbnail image
  isActive      Boolean        @default(true)
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  hotspots      Hotspot[]
  views         PanoramaView[]
  activities    Activity[]

  @@map("panoramas")
}

model Hotspot {
  id          String           @id @default(cuid())
  panoramaId  String
  xPosition   Float            // Percentage position (0-100)
  yPosition   Float            // Percentage position (0-100)
  iconType    String           @default("info")
  iconColor   String           @default("#007bff")
  isActive    Boolean          @default(true)
  order       Int              @default(0)
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  panorama    Panorama         @relation(fields: [panoramaId], references: [id], onDelete: Cascade)
  content     HotspotContent[]

  @@map("hotspots")
}

model HotspotContent {
  id           String   @id @default(cuid())
  hotspotId    String
  languageCode String
  title        String
  subtitle     String?
  description  String   @db.Text
  imageUrl     String?
  linkUrl      String?
  linkText     String?
  order        Int      @default(0)  // Order for slideshow sequence
  // File content fields
  contentType  String?  @default("standard") // 'standard' or 'file'
  fileUrl      String?  // URL to uploaded file
  fileType     String?  // 'image' or 'pdf'
  fileMetadata Json?    // File metadata (originalName, size, etc.)
  // Styling fields stored as JSON
  titleStyle       Json?    // Font styling for title
  subtitleStyle    Json?    // Font styling for subtitle
  descriptionStyle Json?    // Font styling for description
  linkStyle        Json?    // Link styling (button vs link)
  imageStyle       Json?    // Image styling (size and aspect ratio)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  hotspot      Hotspot  @relation(fields: [hotspotId], references: [id], onDelete: Cascade)
  language     Language @relation(fields: [languageCode], references: [code])

  // Removed unique constraint to allow multiple products per language
  // Added index for efficient querying
  @@index([hotspotId, languageCode, order])
  @@map("hotspot_content")
}

model Language {
  code           String           @id
  name           String
  nativeName     String
  isActive       Boolean          @default(true)
  isDefault      Boolean          @default(false)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  hotspotContent HotspotContent[]

  @@map("languages")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  firstName String?
  lastName  String?
  role      String   @default("admin")
  isActive  Boolean  @default(true)
  lastLogin DateTime?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  @@map("sessions")
}

model PanoramaView {
  id         String   @id @default(cuid())
  panoramaId String
  ipAddress  String?
  userAgent  String?
  referrer   String?
  viewedAt   DateTime @default(now())
  panorama   Panorama @relation(fields: [panoramaId], references: [id], onDelete: Cascade)

  @@map("panorama_views")
}

model Activity {
  id          String    @id @default(cuid())
  type        String    // 'panorama_created', 'hotspot_created', 'content_created', etc.
  title       String
  description String?
  entityType  String?   // 'panorama', 'hotspot', 'content', 'language'
  entityId    String?
  panoramaId  String?
  userId      String?
  metadata    Json?     // Additional data as JSON
  createdAt   DateTime  @default(now())
  panorama    Panorama? @relation(fields: [panoramaId], references: [id], onDelete: SetNull)

  @@map("activities")
}
