module.exports = {
  i18n: {
    defaultLocale: "en",
    locales: ["en", "da", "de", "fr", "es"],
    localeDetection: false,
  },
  fallbackLng: "en",
  debug: process.env.NODE_ENV === "development",
  reloadOnPrerender: process.env.NODE_ENV === "development",

  // Namespace configuration
  ns: ["common", "viewer", "admin"],
  defaultNS: "common",

  // Interpolation
  interpolation: {
    escapeValue: false,
  },

  // React configuration
  react: {
    useSuspense: false,
  },
};
