name: Deploy to Server
on:
  push:
    branches:
      - main
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Create .ssh directory
        run: mkdir -p ~/.ssh
      - name: Add server to known hosts
        run: ssh-keyscan -H ************ >> ~/.ssh/known_hosts
      - name: Deploy to server
        env:
          SSH_AUTH_SOCK: /tmp/ssh_agent.sock
        run: |
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > private_key
          chmod 600 private_key
          eval "$(ssh-agent -s)"
          ssh-add private_key
          ssh -o StrictHostKeyChecking=no timo@************ << 'EOF'
            cd /home/<USER>/Projects/iduview
            git fetch origin
            git reset --hard origin/main  # Reset instead of pull
            bash deploy.sh
          EOF
          rm private_key
