# Docker Environment Variables
# This file overrides production settings for local Docker development
NODE_ENV=production

# Database
DATABASE_URL=postgresql://root:Onamission%23007@************:5432/iduna_db

# API Configuration - Use server IP for Docker production
BACKEND_PORT=5000
FRONTEND_PORT=3000
API_BASE_URL=http://************:5000
FRONTEND_BASE_URL=http://************:3000

# Authentication
JWT_SECRET=CHANGE_THIS_IN_PRODUCTION_SUPER_SECRET_KEY_2024
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_EXPIRES_IN=30d

# Redis - Use container name for Docker networking (internal port 6379, external port 6380)
REDIS_URL=redis://redis:6379

# File Upload
MAX_FILE_SIZE=50MB
UPLOAD_PATH=/app/uploads

# CORS - Allow server IP for Docker production
CORS_ORIGIN=http://************:3000

# WordPress Integration
WORDPRESS_INTEGRATION_URL=http://localhost:8080

# Logging
LOG_LEVEL=info

# SSL (not needed for local Docker)
# SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
# SSL_KEY_PATH=/etc/nginx/ssl/key.pem
