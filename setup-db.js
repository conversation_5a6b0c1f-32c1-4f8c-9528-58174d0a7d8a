#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

// Set environment variables
process.env.NODE_ENV = 'development';
process.env.DATABASE_URL = 'postgresql://root:Onamission%23007@37.27.84.251:5432/iduna_db';

console.log('🗄️  Setting up database...\n');

const runCommand = (command, args, cwd) => {
  return new Promise((resolve, reject) => {
    console.log(`📡 Running: ${command} ${args.join(' ')}`);
    const child = spawn(command, args, {
      cwd,
      stdio: 'inherit',
      shell: true,
      env: { ...process.env }
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with code ${code}`));
      }
    });
  });
};

async function setupDatabase() {
  const backendDir = path.join(__dirname, 'backend');
  
  try {
    console.log('1️⃣  Generating Prisma client...');
    await runCommand('npx', ['prisma', 'generate'], backendDir);
    
    console.log('\n2️⃣  Running database migrations...');
    await runCommand('npx', ['prisma', 'db', 'push'], backendDir);
    
    console.log('\n3️⃣  Seeding database...');
    await runCommand('npx', ['ts-node', 'prisma/seed.ts'], backendDir);
    
    console.log('\n✅ Database setup completed successfully!');
    console.log('🎯 You can now start the development server with: npm run dev:local');
    
  } catch (error) {
    console.error('\n❌ Database setup failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Check if PostgreSQL is running and accessible');
    console.log('2. Verify the DATABASE_URL connection string');
    console.log('3. Ensure the database "iduna_db" exists');
    process.exit(1);
  }
}

setupDatabase();
