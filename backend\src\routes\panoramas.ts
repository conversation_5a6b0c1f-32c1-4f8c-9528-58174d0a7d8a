import express, { Request, Response, NextFunction } from "express";
import { body, param, query, validationResult } from "express-validator";
import { prisma } from "../utils/database";
import { authenticate, authorize, AuthRequest } from "../middleware/auth";
import { getCache, setCache, clearCachePattern } from "../utils/redis";
import logger from "../utils/logger";
import { logPanoramaActivity } from "../utils/activityLogger";

const router = express.Router();

// Validation rules
const createPanoramaValidation = [
  body("title").trim().isLength({ min: 1, max: 255 }),
  body("description").optional().trim().isLength({ max: 1000 }),
  body("filename").trim().isLength({ min: 1 }),
  body("width").isInt({ min: 1 }),
  body("height").isInt({ min: 1 }),
  body("fileSize").isInt({ min: 1 }),
  body("mimeType").isIn(["image/jpeg", "image/jpg", "image/png", "image/webp"]),
  body("thumbnailPath").optional().trim().isLength({ min: 1 }),
];

const updatePanoramaValidation = [
  param("id").isString(),
  body("title").optional().trim().isLength({ min: 1, max: 255 }),
  body("description").optional().trim().isLength({ max: 1000 }),
  body("isActive").optional().isBoolean(),
];

// GET /api/panoramas
router.get(
  "/",
  async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { page = 1, limit = 10, search, isActive } = req.query;
      const skip = (Number(page) - 1) * Number(limit);

      // Build where clause
      const where: any = {};
      if (search) {
        where.OR = [
          { title: { contains: search as string, mode: "insensitive" } },
          { description: { contains: search as string, mode: "insensitive" } },
        ];
      }
      if (isActive !== undefined) {
        where.isActive = isActive === "true";
      }

      // Check cache
      const cacheKey = `panoramas:${JSON.stringify({
        page,
        limit,
        search,
        isActive,
      })}`;
      const cached = await getCache(cacheKey);
      if (cached) {
        res.json(cached);
        return;
      }

      // Get panoramas with pagination
      const [panoramas, total] = await Promise.all([
        prisma.panorama.findMany({
          where,
          skip,
          take: Number(limit),
          orderBy: { createdAt: "desc" },
          include: {
            _count: {
              select: { hotspots: true },
            },
          },
        }),
        prisma.panorama.count({ where }),
      ]);

      // Add imageUrl to each panorama
      const panoramasWithImageUrl = panoramas.map((panorama) => ({
        ...panorama,
        imageUrl: `/uploads/${panorama.filename}`,
      }));

      const result = {
        success: true,
        data: {
          panoramas: panoramasWithImageUrl,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total,
            pages: Math.ceil(total / Number(limit)),
          },
        },
      };

      // Cache for 5 minutes
      await setCache(cacheKey, result, 300);

      res.json(result);
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/panoramas/:id
router.get(
  "/:id",
  [param("id").isString()],
  async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: { message: "Validation failed", details: errors.array() },
        });
        return;
      }

      const { id } = req.params;

      // Check cache
      const cacheKey = `panorama:${id}`;
      const cached = await getCache(cacheKey);
      if (cached) {
        res.json(cached);
        return;
      }

      const panorama = await prisma.panorama.findUnique({
        where: { id },
        include: {
          hotspots: {
            where: { isActive: true },
            orderBy: { order: "asc" },
            include: {
              content: {
                include: {
                  language: true,
                },
              },
            },
          },
        },
      });

      if (!panorama) {
        res.status(404).json({
          success: false,
          error: { message: "Panorama not found" },
        });
        return;
      }

      // Add imageUrl to panorama
      const panoramaWithImageUrl = {
        ...panorama,
        imageUrl: `/uploads/${panorama.filename}`,
      };

      const result = {
        success: true,
        data: { panorama: panoramaWithImageUrl },
      };

      // Cache for 10 minutes
      await setCache(cacheKey, result, 600);

      res.json(result);
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/panoramas
router.post(
  "/",
  [authenticate, authorize("admin"), ...createPanoramaValidation],
  async (
    req: AuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: { message: "Validation failed", details: errors.array() },
        });
        return;
      }

      const {
        title,
        description,
        filename,
        width,
        height,
        fileSize,
        mimeType,
        thumbnailPath,
      } = req.body;

      const panorama = await prisma.panorama.create({
        data: {
          title,
          description,
          filename,
          width,
          height,
          fileSize,
          mimeType,
          thumbnailPath,
        },
      });

      // Add imageUrl to response
      const panoramaWithImageUrl = {
        ...panorama,
        imageUrl: `/uploads/${panorama.filename}`,
      };

      // Clear cache
      await clearCachePattern("panoramas:*");

      // Log activity
      await logPanoramaActivity.created(
        panorama.id,
        panorama.title,
        req.user?.id
      );

      logger.info(`Panorama created: ${panorama.id}`);

      res.status(201).json({
        success: true,
        data: { panorama: panoramaWithImageUrl },
      });
    } catch (error) {
      next(error);
    }
  }
);

// PUT /api/panoramas/:id
router.put(
  "/:id",
  [authenticate, authorize("admin"), ...updatePanoramaValidation],
  async (
    req: AuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: { message: "Validation failed", details: errors.array() },
        });
        return;
      }

      const { id } = req.params;
      const { replaceImage, ...updateData } = req.body;

      // If replacing image, delete all associated hotspots first
      if (replaceImage) {
        await prisma.hotspot.deleteMany({
          where: { panoramaId: id },
        });
        logger.info(
          `Deleted all hotspots for panorama ${id} due to image replacement`
        );
      }

      const panorama = await prisma.panorama.update({
        where: { id },
        data: updateData,
      });

      // Clear cache
      await clearCachePattern(`panorama:${id}`);
      await clearCachePattern("panoramas:*");

      logger.info(`Panorama updated: ${panorama.id}`);

      res.json({
        success: true,
        data: { panorama },
      });
    } catch (error) {
      next(error);
    }
  }
);

// DELETE /api/panoramas/:id
router.delete(
  "/:id",
  [authenticate, authorize("admin"), param("id").isString()],
  async (
    req: AuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: { message: "Validation failed", details: errors.array() },
        });
        return;
      }

      const { id } = req.params;

      await prisma.panorama.delete({
        where: { id },
      });

      // Clear cache
      await clearCachePattern(`panorama:${id}`);
      await clearCachePattern("panoramas:*");

      logger.info(`Panorama deleted: ${id}`);

      res.json({
        success: true,
        data: { message: "Panorama deleted successfully" },
      });
    } catch (error) {
      next(error);
    }
  }
);

export default router;
