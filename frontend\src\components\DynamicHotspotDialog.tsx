// frontend\src\components\DynamicHotspotDialog.tsx

import React from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ExternalLink, X, FileText, Image } from "lucide-react";
import { useAppTranslation } from "@/hooks/useAppTranslation";
import FileContentDialog from "./FileContentDialog";

interface HotspotContentStyle {
  fontSize?: string;
  fontWeight?: string;
  fontStyle?: string;
  textDecoration?: string;
  fontFamily?: string;
  color?: string;
  textAlign?: "left" | "center" | "right";
  paddingTop?: string;
  paddingBottom?: string;
}

interface HotspotContent {
  id?: string;
  title: string;
  subtitle?: string;
  description: string;
  imageUrl?: string;
  linkUrl?: string;
  linkText?: string;
  languageCode: string;
  // File content properties
  contentType?: "standard" | "file";
  fileUrl?: string;
  fileType?: "image" | "pdf";
  fileMetadata?: {
    originalName?: string;
    originalUrl?: string;
    size?: number;
    mimeType?: string;
  };
  // Styling properties
  titleStyle?: HotspotContentStyle;
  subtitleStyle?: HotspotContentStyle;
  descriptionStyle?: HotspotContentStyle;
  linkStyle?: {
    type: "button" | "link";
    size?: "small" | "medium" | "large";
  };
  imageStyle?: {
    size: "small" | "medium" | "large";
    aspectRatio: "auto" | "16:9" | "4:3" | "1:1";
  };
}

interface DynamicHotspotDialogProps {
  content: HotspotContent;
  onClose?: () => void;
  showCloseButton?: boolean;
  isPreview?: boolean;
  className?: string;
}

export const DynamicHotspotDialog: React.FC<DynamicHotspotDialogProps> = ({
  content,
  onClose,
  showCloseButton = true,
  isPreview = false,
  className = "",
}) => {
  const { t } = useAppTranslation();

  // If content is file type, render FileContentDialog or preview
  if (content.contentType === "file") {
    if (!isPreview) {
      return (
        <FileContentDialog
          isOpen={true}
          onClose={onClose || (() => {})}
          content={content}
        />
      );
    } else {
      // Preview mode for file content
      const fileUrl = content.fileUrl?.startsWith("http")
        ? content.fileUrl
        : `${process.env.NEXT_PUBLIC_API_URL}${content.fileUrl}`;

      return (
        <div className={`bg-white rounded-lg shadow-lg border ${className}`}>
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">
                {content.title || "File Content Preview"}
              </h3>
              <Badge variant="secondary">
                {content.languageCode?.toUpperCase() || "EN"}
              </Badge>
            </div>

            <div className="border-2 border-dashed border-blue-300 bg-blue-50 rounded-lg p-8 text-center">
              {content.fileType === "pdf" ? (
                <div className="flex flex-col items-center space-y-2">
                  <FileText className="h-12 w-12 text-red-600" />
                  <p className="font-medium">PDF Document</p>
                  <p className="text-sm text-muted-foreground">
                    {content.fileMetadata?.originalName || "PDF file"}
                  </p>
                  <p className="text-xs text-blue-700">
                    Preview not available - will display full-screen when
                    clicked
                  </p>
                </div>
              ) : (
                <div className="flex flex-col items-center space-y-2">
                  {fileUrl ? (
                    <img
                      src={fileUrl}
                      alt={content.title || "File preview"}
                      className="max-w-full max-h-48 object-contain rounded"
                      onError={(e) => {
                        e.currentTarget.style.display = "none";
                        const nextElement = e.currentTarget
                          .nextElementSibling as HTMLElement;
                        if (nextElement) {
                          nextElement.style.display = "block";
                        }
                      }}
                    />
                  ) : (
                    <Image className="h-12 w-12 text-blue-600" />
                  )}
                  <div style={{ display: "none" }}>
                    <Image className="h-12 w-12 text-blue-600" />
                  </div>
                  <p className="font-medium">Image File</p>
                  <p className="text-sm text-muted-foreground">
                    {content.fileMetadata?.originalName || "Image file"}
                  </p>
                  <p className="text-xs text-blue-700">
                    Will display full-screen when clicked
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      );
    }
  }

  // Debug styling data
  console.log("🎨 DynamicHotspotDialog received content:", content);
  console.log("🎨 File content data:", {
    contentType: content.contentType,
    fileUrl: content.fileUrl,
    fileType: content.fileType,
    fileMetadata: content.fileMetadata,
  });
  console.log("🎨 Styling data:", {
    titleStyle: content.titleStyle,
    subtitleStyle: content.subtitleStyle,
    descriptionStyle: content.descriptionStyle,
    linkStyle: content.linkStyle,
    imageStyle: content.imageStyle,
  });

  // Default styles
  const defaultTitleStyle: HotspotContentStyle = {
    fontSize: "20px",
    fontWeight: "700",
    fontStyle: "normal",
    textDecoration: "none",
    fontFamily: "Inter, sans-serif",
    color: "#000000",
    textAlign: "left",
    paddingTop: "0px",
    paddingBottom: "8px",
  };

  const defaultSubtitleStyle: HotspotContentStyle = {
    fontSize: "16px",
    fontWeight: "500",
    fontStyle: "normal",
    textDecoration: "none",
    fontFamily: "Inter, sans-serif",
    color: "#6b7280",
    textAlign: "left",
    paddingTop: "0px",
    paddingBottom: "8px",
  };

  const defaultDescriptionStyle: HotspotContentStyle = {
    fontSize: "14px",
    fontWeight: "400",
    fontStyle: "normal",
    textDecoration: "none",
    fontFamily: "Inter, sans-serif",
    color: "#374151",
    textAlign: "left",
    paddingTop: "0px",
    paddingBottom: "16px",
  };

  // Merge with custom styles
  const titleStyle = { ...defaultTitleStyle, ...content.titleStyle };
  const subtitleStyle = { ...defaultSubtitleStyle, ...content.subtitleStyle };
  const descriptionStyle = {
    ...defaultDescriptionStyle,
    ...content.descriptionStyle,
  };

  const handleLinkClick = (e: React.MouseEvent) => {
    if (isPreview) {
      e.preventDefault();
      return;
    }
    // For actual hotspot dialogs, allow normal link behavior
  };

  // Get button size class based on linkStyle.size
  const getButtonSize = () => {
    switch (content.linkStyle?.size) {
      case "small":
        return "sm";
      case "large":
        return "lg";
      case "medium":
      default:
        return "default";
    }
  };

  // Get link size class based on linkStyle.size
  const getLinkSizeClass = () => {
    switch (content.linkStyle?.size) {
      case "small":
        return "text-sm";
      case "large":
        return "text-lg";
      case "medium":
      default:
        return "text-base";
    }
  };

  // Get image size and aspect ratio styles
  const getImageStyles = () => {
    const size = content.imageStyle?.size || "medium";
    const aspectRatio = content.imageStyle?.aspectRatio || "auto";

    // Width based on size
    let width = "100%";
    switch (size) {
      case "small":
        width = "60%";
        break;
      case "large":
        width = "100%";
        break;
      case "medium":
      default:
        width = "80%";
        break;
    }

    // Aspect ratio handling
    let aspectRatioStyle = {};
    if (aspectRatio !== "auto") {
      switch (aspectRatio) {
        case "16:9":
          aspectRatioStyle = { aspectRatio: "16/9" };
          break;
        case "4:3":
          aspectRatioStyle = { aspectRatio: "4/3" };
          break;
        case "1:1":
          aspectRatioStyle = { aspectRatio: "1/1" };
          break;
      }
    }

    const finalStyles = {
      width,
      maxWidth: width,
      margin: "0 auto",
      ...aspectRatioStyle,
    };

    return finalStyles;
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg border ${className}`}>
      {/* Header */}
      <div className="p-6 pb-4">
        <div className="flex items-start justify-between">
          <div className="space-y-2 flex-1">
            {/* Title */}
            <h3
              style={{
                fontFamily: titleStyle.fontFamily,
                fontSize: titleStyle.fontSize,
                fontWeight: titleStyle.fontWeight,
                fontStyle: titleStyle.fontStyle,
                textDecoration: titleStyle.textDecoration,
                color: titleStyle.color,
                textAlign: titleStyle.textAlign,
                paddingTop: titleStyle.paddingTop,
                paddingBottom: titleStyle.paddingBottom,
                margin: 0,
                lineHeight: "1.2",
              }}
            >
              {content.title || "Sample Title"}
            </h3>

            {/* Subtitle */}
            {content.subtitle && (
              <p
                style={{
                  fontFamily: subtitleStyle.fontFamily,
                  fontSize: subtitleStyle.fontSize,
                  fontWeight: subtitleStyle.fontWeight,
                  fontStyle: subtitleStyle.fontStyle,
                  textDecoration: subtitleStyle.textDecoration,
                  color: subtitleStyle.color,
                  textAlign: subtitleStyle.textAlign,
                  paddingTop: subtitleStyle.paddingTop,
                  paddingBottom: subtitleStyle.paddingBottom,
                  margin: 0,
                  lineHeight: "1.3",
                }}
              >
                {content.subtitle}
              </p>
            )}
          </div>

          {/* Badge - Optional category indicator */}
          <Badge variant="secondary" className="ml-4">
            {content.languageCode?.toUpperCase() || "EN"}
          </Badge>
        </div>
      </div>

      {/* Content */}
      <div className="px-6 space-y-4">
        {/* Image */}
        {content.imageUrl && (
          <div className="flex justify-center">
            <div
              className="bg-muted rounded-lg overflow-hidden"
              style={getImageStyles()}
            >
              <img
                src={
                  content.imageUrl.startsWith("http")
                    ? content.imageUrl
                    : `${
                        process.env.NEXT_PUBLIC_API_URL ||
                        "http://localhost:5000"
                      }${content.imageUrl}`
                }
                alt={content.title || "Hotspot image"}
                className={`w-full h-auto ${
                  content.imageStyle?.aspectRatio === "auto"
                    ? "object-contain"
                    : "object-cover"
                }`}
                style={{
                  maxHeight:
                    content.imageStyle?.size === "small"
                      ? "250px"
                      : content.imageStyle?.size === "large"
                      ? "500px"
                      : "350px",
                  imageRendering: "auto",
                }}
                onError={(e) => {
                  e.currentTarget.src = "/images/placeholder-image.png";
                }}
                loading="lazy"
              />
            </div>
          </div>
        )}

        {/* Description */}
        {content.description && (
          <div
            style={{
              fontFamily: descriptionStyle.fontFamily,
              fontSize: descriptionStyle.fontSize,
              fontWeight: descriptionStyle.fontWeight,
              fontStyle: descriptionStyle.fontStyle,
              textDecoration: descriptionStyle.textDecoration,
              color: descriptionStyle.color,
              textAlign: descriptionStyle.textAlign,
              paddingTop: descriptionStyle.paddingTop,
              paddingBottom: descriptionStyle.paddingBottom,
              lineHeight: "1.5",
            }}
          >
            {content.description}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2 pb-6">
          {/* Link/Button */}
          {content.linkUrl && content.linkText && (
            <div className="flex-1">
              {content.linkStyle?.type === "link" ? (
                <a
                  href={isPreview ? "#" : content.linkUrl}
                  target={isPreview ? undefined : "_blank"}
                  rel={isPreview ? undefined : "noopener noreferrer"}
                  onClick={handleLinkClick}
                  className={`text-blue-600 underline hover:text-blue-800 inline-flex items-center ${getLinkSizeClass()}`}
                >
                  <ExternalLink className="mr-2 h-4 w-4" />
                  {content.linkText}
                </a>
              ) : (
                <Button
                  asChild={!isPreview}
                  className="w-full"
                  size={getButtonSize() as any}
                  onClick={isPreview ? handleLinkClick : undefined}
                >
                  {isPreview ? (
                    <span className="inline-flex items-center">
                      <ExternalLink className="mr-2 h-4 w-4" />
                      {content.linkText}
                    </span>
                  ) : (
                    <a
                      href={content.linkUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center"
                    >
                      <ExternalLink className="mr-2 h-4 w-4" />
                      {content.linkText}
                    </a>
                  )}
                </Button>
              )}
            </div>
          )}

          {/* Close Button */}
          {showCloseButton && onClose && (
            <Button variant="outline" onClick={onClose}>
              <X className="mr-2 h-4 w-4" />
              {t("admin.panoramaViewer.hotspotDialog.close", "Close")}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};
