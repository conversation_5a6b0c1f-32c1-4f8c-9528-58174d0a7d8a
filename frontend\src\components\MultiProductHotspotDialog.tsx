import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { HotspotContent } from "@/types/hotspot";
import { useAppTranslation } from "@/hooks/useAppTranslation";

interface MultiProductHotspotDialogProps {
  isOpen: boolean;
  onClose: () => void;
  products: HotspotContent[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
}

export default function MultiProductHotspotDialog({
  isOpen,
  onClose,
  products,
  autoPlay = true,
  autoPlayInterval = 5000,
}: MultiProductHotspotDialogProps) {
  const { t } = useAppTranslation();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(autoPlay);

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying || products.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % products.length);
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [isAutoPlaying, products.length, autoPlayInterval]);

  // Pause auto-play on user interaction
  const handleUserInteraction = () => {
    setIsAutoPlaying(false);
  };

  // Navigation functions
  const goToNext = () => {
    handleUserInteraction();
    setCurrentIndex((prev) => (prev + 1) % products.length);
  };

  const goToPrevious = () => {
    handleUserInteraction();
    setCurrentIndex((prev) => (prev - 1 + products.length) % products.length);
  };

  const goToSlide = (index: number) => {
    handleUserInteraction();
    setCurrentIndex(index);
  };

  // Reset when dialog opens/closes
  useEffect(() => {
    if (isOpen) {
      setCurrentIndex(0);
      setIsAutoPlaying(autoPlay);
    }
  }, [isOpen, autoPlay]);

  if (!products || products.length === 0) {
    return null;
  }

  const currentProduct = products[currentIndex];

  // Determine dialog size based on content type
  const isFileContent = currentProduct?.contentType === "file";
  const dialogClassName = isFileContent
    ? "max-w-[95vw] max-h-[95vh] w-fit h-fit p-0 overflow-hidden"
    : "max-w-2xl max-h-[90vh] overflow-hidden";

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={dialogClassName}>
        {!isFileContent && (
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">
              {currentProduct.title}
            </DialogTitle>
            <DialogDescription className="sr-only">
              Hotspot content dialog showing product information
            </DialogDescription>
          </DialogHeader>
        )}

        <div className="relative">
          {/* File Content Display */}
          {isFileContent ? (
            <div className="relative">
              {(() => {
                const fileUrl = currentProduct.fileUrl?.startsWith("http")
                  ? currentProduct.fileUrl
                  : `${process.env.NEXT_PUBLIC_API_URL}${currentProduct.fileUrl}`;
                console.log("🔍 File URL Debug:", {
                  originalFileUrl: currentProduct.fileUrl,
                  constructedUrl: fileUrl,
                  fileType: currentProduct.fileType,
                  apiUrl: process.env.NEXT_PUBLIC_API_URL,
                });
                return null;
              })()}
              {currentProduct.fileType === "pdf" ? (
                <iframe
                  src={`${
                    currentProduct.fileUrl?.startsWith("http")
                      ? currentProduct.fileUrl
                      : `${process.env.NEXT_PUBLIC_API_URL}${currentProduct.fileUrl}`
                  }#toolbar=0&navpanes=0&scrollbar=0`}
                  className="w-[90vw] h-[80vh] md:w-[80vw] md:h-[85vh] lg:w-[70vw] lg:h-[90vh] border-0 rounded-lg"
                  title={currentProduct.title}
                />
              ) : (
                <img
                  src={
                    currentProduct.fileUrl?.startsWith("http")
                      ? currentProduct.fileUrl
                      : `${process.env.NEXT_PUBLIC_API_URL}${currentProduct.fileUrl}`
                  }
                  alt={currentProduct.title}
                  className="max-w-[95vw] max-h-[95vh] w-auto h-auto object-contain rounded-lg shadow-lg"
                  style={{ imageRendering: "auto" }}
                />
              )}
            </div>
          ) : (
            /* Standard Content Display */
            <div className="space-y-4">
              {/* Subtitle */}
              {currentProduct.subtitle && (
                <p
                  className="text-lg text-muted-foreground"
                  style={currentProduct.subtitleStyle}
                >
                  {currentProduct.subtitle}
                </p>
              )}

              {/* Image */}
              {currentProduct.imageUrl && (
                <div className="relative flex justify-center">
                  <div
                    className={`relative overflow-hidden rounded-lg ${
                      currentProduct.imageStyle?.size === "small"
                        ? "w-3/5"
                        : currentProduct.imageStyle?.size === "large"
                        ? "w-full"
                        : "w-4/5"
                    }`}
                    style={{
                      aspectRatio:
                        currentProduct.imageStyle?.aspectRatio === "auto"
                          ? "auto"
                          : currentProduct.imageStyle?.aspectRatio === "16:9"
                          ? "16/9"
                          : currentProduct.imageStyle?.aspectRatio === "4:3"
                          ? "4/3"
                          : currentProduct.imageStyle?.aspectRatio === "1:1"
                          ? "1/1"
                          : "auto",
                    }}
                  >
                    <img
                      src={currentProduct.imageUrl}
                      alt={currentProduct.title}
                      className={`w-full h-full ${
                        currentProduct.imageStyle?.aspectRatio === "auto"
                          ? "object-contain"
                          : "object-cover"
                      }`}
                      style={{
                        maxHeight:
                          currentProduct.imageStyle?.size === "small"
                            ? "250px"
                            : currentProduct.imageStyle?.size === "large"
                            ? "500px"
                            : "350px",
                        imageRendering: "auto",
                      }}
                      loading="lazy"
                    />
                  </div>
                </div>
              )}

              {/* Description */}
              <div
                className="prose prose-sm max-w-none text-sm leading-relaxed"
                style={currentProduct.descriptionStyle}
                dangerouslySetInnerHTML={{ __html: currentProduct.description }}
              />

              {/* Link/Button */}
              {currentProduct.linkUrl && currentProduct.linkText && (
                <div className="pt-2">
                  {currentProduct.linkStyle?.type === "button" ? (
                    <Button
                      asChild
                      size={
                        currentProduct.linkStyle.size === "small"
                          ? "sm"
                          : currentProduct.linkStyle.size === "large"
                          ? "lg"
                          : "default"
                      }
                      className="w-full sm:w-auto"
                    >
                      <a
                        href={currentProduct.linkUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {currentProduct.linkText}
                      </a>
                    </Button>
                  ) : (
                    <a
                      href={currentProduct.linkUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`text-blue-600 hover:text-blue-800 underline ${
                        currentProduct.linkStyle?.size === "small"
                          ? "text-sm"
                          : currentProduct.linkStyle?.size === "large"
                          ? "text-lg"
                          : "text-base"
                      }`}
                    >
                      {currentProduct.linkText}
                    </a>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Navigation Arrows (only show if multiple products) */}
          {products.length > 1 && (
            <>
              <Button
                variant="ghost"
                size="sm"
                onClick={goToPrevious}
                className="absolute left-2 top-1/2 -translate-y-1/2 h-10 w-10 p-0 bg-white/80 hover:bg-white/90 shadow-md"
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={goToNext}
                className="absolute right-2 top-1/2 -translate-y-1/2 h-10 w-10 p-0 bg-white/80 hover:bg-white/90 shadow-md"
              >
                <ChevronRight className="h-5 w-5" />
              </Button>
            </>
          )}
        </div>

        {/* Dot Indicators (only show if multiple products) */}
        {products.length > 1 && (
          <div className="flex justify-center space-x-2 pt-4">
            {products.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`h-3 w-3 rounded-full transition-all duration-200 ${
                  index === currentIndex
                    ? "bg-blue-600 scale-110"
                    : "bg-gray-300 hover:bg-gray-400"
                }`}
                aria-label={`Go to product ${index + 1}`}
              />
            ))}
          </div>
        )}

        {/* Product Counter */}
        {products.length > 1 && (
          <div className="text-center text-sm text-muted-foreground pt-2">
            {currentIndex + 1} / {products.length}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
