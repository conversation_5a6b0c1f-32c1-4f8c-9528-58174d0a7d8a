import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type AppLanguage = 'en' | 'da';

interface LanguageState {
  language: AppLanguage;
  setLanguage: (language: AppLanguage) => void;
  getLanguageLabel: (language: AppLanguage) => string;
  getLanguageNative: (language: AppLanguage) => string;
}

const languageLabels: Record<AppLanguage, string> = {
  en: 'English',
  da: 'Danish',
};

const languageNative: Record<AppLanguage, string> = {
  en: 'English',
  da: 'Dansk',
};

export const useLanguageStore = create<LanguageState>()(
  persist(
    (set, get) => ({
      language: 'en',
      setLanguage: (language: AppLanguage) => {
        set({ language });
      },
      getLanguageLabel: (language: AppLanguage) => languageLabels[language],
      getLanguageNative: (language: AppLanguage) => languageNative[language],
    }),
    {
      name: 'app-language-storage',
      partialize: (state) => ({ language: state.language }),
    }
  )
);
