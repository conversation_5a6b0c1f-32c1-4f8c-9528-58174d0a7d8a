import express, { Request, Response, NextFunction } from "express";
import { body, param, validationResult } from "express-validator";
import { prisma } from "../utils/database";
import { authenticate, authorize, AuthRequest } from "../middleware/auth";
import { getCache, setCache, clearCachePattern } from "../utils/redis";
import logger from "../utils/logger";

const router = express.Router();

// Validation rules
const createLanguageValidation = [
  body("code")
    .isLength({ min: 2, max: 5 })
    .matches(/^[a-z]{2}(-[A-Z]{2})?$/),
  body("name").trim().isLength({ min: 1, max: 100 }),
  body("nativeName").trim().isLength({ min: 1, max: 100 }),
  body("isDefault").optional().isBoolean(),
];

const updateLanguageValidation = [
  param("code").isLength({ min: 2, max: 5 }),
  body("name").optional().trim().isLength({ min: 1, max: 100 }),
  body("nativeName").optional().trim().isLength({ min: 1, max: 100 }),
  body("isActive").optional().isBoolean(),
  body("isDefault").optional().isBoolean(),
];

// GET /api/languages
router.get(
  "/",
  async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { active } = req.query;

      // Check cache
      const cacheKey = `languages:${active || "all"}`;
      const cached = await getCache(cacheKey);
      if (cached) {
        res.json(cached);
        return;
      }

      const where: any = {};
      if (active === "true") {
        where.isActive = true;
      }

      const languages = await prisma.language.findMany({
        where,
        orderBy: [{ isDefault: "desc" }, { name: "asc" }],
        include: {
          _count: {
            select: { hotspotContent: true },
          },
        },
      });

      const result = {
        success: true,
        data: { languages },
      };

      // Cache for 30 minutes
      await setCache(cacheKey, result, 1800);

      res.json(result);
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/languages/:code
router.get(
  "/:code",
  [param("code").isLength({ min: 2, max: 5 })],
  async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: { message: "Validation failed", details: errors.array() },
        });
        return;
      }

      const { code } = req.params;

      const language = await prisma.language.findUnique({
        where: { code },
        include: {
          _count: {
            select: { hotspotContent: true },
          },
        },
      });

      if (!language) {
        res.status(404).json({
          success: false,
          error: { message: "Language not found" },
        });
        return;
      }

      res.json({
        success: true,
        data: { language },
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/languages
router.post(
  "/",
  [authenticate, authorize("admin"), ...createLanguageValidation],
  async (
    req: AuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: { message: "Validation failed", details: errors.array() },
        });
        return;
      }

      const { code, name, nativeName, isDefault = false } = req.body;

      // If this is set as default, unset other defaults
      if (isDefault) {
        await prisma.language.updateMany({
          where: { isDefault: true },
          data: { isDefault: false },
        });
      }

      const language = await prisma.language.create({
        data: {
          code,
          name,
          nativeName,
          isDefault,
        },
      });

      // Clear cache
      await clearCachePattern("languages:*");

      logger.info(`Language created: ${language.code}`);

      res.status(201).json({
        success: true,
        data: { language },
      });
    } catch (error) {
      next(error);
    }
  }
);

// PUT /api/languages/:code
router.put(
  "/:code",
  [authenticate, authorize("admin"), ...updateLanguageValidation],
  async (
    req: AuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: { message: "Validation failed", details: errors.array() },
        });
        return;
      }

      const { code } = req.params;
      const updateData = req.body;

      // If this is set as default, unset other defaults
      if (updateData.isDefault) {
        await prisma.language.updateMany({
          where: {
            isDefault: true,
            code: { not: code },
          },
          data: { isDefault: false },
        });
      }

      const language = await prisma.language.update({
        where: { code },
        data: updateData,
      });

      // Clear cache
      await clearCachePattern("languages:*");

      logger.info(`Language updated: ${language.code}`);

      res.json({
        success: true,
        data: { language },
      });
    } catch (error) {
      next(error);
    }
  }
);

// DELETE /api/languages/:code
router.delete(
  "/:code",
  [
    authenticate,
    authorize("admin"),
    param("code").isLength({ min: 2, max: 5 }),
  ],
  async (
    req: AuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: { message: "Validation failed", details: errors.array() },
        });
        return;
      }

      const { code } = req.params;

      // Check if language has content
      const contentCount = await prisma.hotspotContent.count({
        where: { languageCode: code },
      });

      if (contentCount > 0) {
        res.status(400).json({
          success: false,
          error: { message: "Cannot delete language with existing content" },
        });
        return;
      }

      // Check if it's the default language
      const language = await prisma.language.findUnique({
        where: { code },
      });

      if (language?.isDefault) {
        res.status(400).json({
          success: false,
          error: { message: "Cannot delete default language" },
        });
        return;
      }

      await prisma.language.delete({
        where: { code },
      });

      // Clear cache
      await clearCachePattern("languages:*");

      logger.info(`Language deleted: ${code}`);

      res.json({
        success: true,
        data: { message: "Language deleted successfully" },
      });
    } catch (error) {
      next(error);
    }
  }
);

export default router;
