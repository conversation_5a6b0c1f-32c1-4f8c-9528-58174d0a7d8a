#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting IduView Complete System...\n');

const runCommand = (command, args, cwd, label) => {
  console.log(`📡 Starting ${label}...`);
  const child = spawn(command, args, {
    cwd,
    stdio: 'inherit',
    shell: true,
    env: { 
      ...process.env,
      NODE_ENV: 'development',
      DATABASE_URL: 'postgresql://root:Onamission%23007@************:5432/iduna_db',
      NEXT_PUBLIC_API_URL: 'http://localhost:5000',
      NEXT_PUBLIC_FRONTEND_URL: 'http://localhost:3000',
      JWT_SECRET: 'dev_jwt_secret_change_in_production_2024'
    }
  });

  child.on('close', (code) => {
    console.log(`${label} process exited with code ${code}`);
    process.exit(code);
  });

  return child;
};

async function startSystem() {
  try {
    console.log('🔧 Installing frontend dependencies...');
    
    // Install frontend dependencies first
    const install = spawn('npm', ['install'], {
      cwd: path.join(__dirname, 'frontend'),
      stdio: 'inherit',
      shell: true
    });

    install.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Dependencies installed!\n');
        
        // Start backend
        const backend = runCommand('npm', ['run', 'dev'], 
          path.join(__dirname, 'backend'), 'Backend Server');

        // Wait a bit for backend to start, then start frontend
        setTimeout(() => {
          const frontend = runCommand('npm', ['run', 'dev'], 
            path.join(__dirname, 'frontend'), 'Frontend Server');

          // Handle process termination
          process.on('SIGINT', () => {
            console.log('\n🛑 Shutting down servers...');
            backend.kill('SIGINT');
            frontend.kill('SIGINT');
            process.exit(0);
          });

          frontend.on('close', (code) => {
            backend.kill('SIGINT');
            process.exit(code);
          });

          backend.on('close', (code) => {
            frontend.kill('SIGINT');
            process.exit(code);
          });
        }, 3000);

        console.log('\n🎯 System Information:');
        console.log('📱 Frontend: http://localhost:3000');
        console.log('🔧 Backend API: http://localhost:5000');
        console.log('🔑 Admin Login: <EMAIL> / admin123');
        console.log('📁 Place panorama at: frontend/public/panoramas/3d_Kitchen_06.jpg');
        console.log('\n🌟 Features Available:');
        console.log('  ✅ 360° Panorama Viewer with hotspots');
        console.log('  ✅ Beautiful popup with content');
        console.log('  ✅ Admin login and dashboard');
        console.log('  ✅ Language management system');
        console.log('  ✅ Complete authentication');
        console.log('\n⏳ Starting servers...');
      } else {
        console.error('❌ Failed to install dependencies');
        process.exit(1);
      }
    });
    
  } catch (error) {
    console.error('\n❌ System startup failed:', error.message);
    process.exit(1);
  }
}

startSystem();
