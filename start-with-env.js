#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting IduView with correct environment variables...\n');

// Set environment variables
process.env.NODE_ENV = 'development';
process.env.DATABASE_URL = 'postgresql://root:Onamission%23007@************:5432/iduna_db';
process.env.JWT_SECRET = 'dev_jwt_secret_change_in_production_2024';
process.env.NEXT_PUBLIC_API_URL = 'http://localhost:5000';
process.env.NEXT_PUBLIC_FRONTEND_URL = 'http://localhost:3000';

const runCommand = (command, args, cwd, label) => {
  console.log(`📡 Starting ${label}...`);
  const child = spawn(command, args, {
    cwd,
    stdio: 'inherit',
    shell: true,
    env: { ...process.env }
  });

  child.on('close', (code) => {
    console.log(`${label} process exited with code ${code}`);
    process.exit(code);
  });

  return child;
};

async function startServers() {
  try {
    console.log('🔧 Environment Variables:');
    console.log(`   NEXT_PUBLIC_API_URL: ${process.env.NEXT_PUBLIC_API_URL}`);
    console.log(`   NEXT_PUBLIC_FRONTEND_URL: ${process.env.NEXT_PUBLIC_FRONTEND_URL}`);
    console.log(`   NODE_ENV: ${process.env.NODE_ENV}\n`);
    
    // Start backend
    const backend = runCommand('npm', ['run', 'dev'], 
      path.join(__dirname, 'backend'), 'Backend Server');

    // Wait a bit for backend to start, then start frontend
    setTimeout(() => {
      const frontend = runCommand('npm', ['run', 'dev'], 
        path.join(__dirname, 'frontend'), 'Frontend Server');

      // Handle process termination
      process.on('SIGINT', () => {
        console.log('\n🛑 Shutting down servers...');
        backend.kill('SIGINT');
        frontend.kill('SIGINT');
        process.exit(0);
      });

      frontend.on('close', (code) => {
        backend.kill('SIGINT');
        process.exit(code);
      });

      backend.on('close', (code) => {
        frontend.kill('SIGINT');
        process.exit(code);
      });
    }, 3000);

    console.log('\n🎯 System Information:');
    console.log('📱 Frontend: http://localhost:3000');
    console.log('🔧 Backend API: http://localhost:5000');
    console.log('🔑 Admin Login: <EMAIL> / admin123');
    console.log('\n🌟 API Endpoints:');
    console.log('   POST http://localhost:5000/api/auth/login');
    console.log('   GET  http://localhost:5000/api/languages');
    console.log('   GET  http://localhost:5000/api/panoramas');
    console.log('\n⏳ Starting servers...');
    
  } catch (error) {
    console.error('\n❌ System startup failed:', error.message);
    process.exit(1);
  }
}

startServers();
