# IduView Nginx Configuration Setup

## Quick Setup Instructions

1. **Copy the configuration file:**
   ```bash
   sudo cp iduview-nginx.conf /etc/nginx/sites-available/iduview
   ```

2. **Edit the configuration with your domain:**
   ```bash
   sudo nano /etc/nginx/sites-available/iduview
   # Replace 'your-domain.com' with your actual domain
   ```

3. **Enable the site:**
   ```bash
   sudo ln -s /etc/nginx/sites-available/iduview /etc/nginx/sites-enabled/
   ```

4. **Test the configuration:**
   ```bash
   sudo nginx -t
   ```

5. **Reload Nginx:**
   ```bash
   sudo systemctl reload nginx
   ```

## Configuration Details

- **Frontend**: Proxies to `localhost:3000` (Next.js)
- **Backend API**: Proxies `/api/` to `localhost:5000`
- **Static Files**: Serves uploads and panoramas directly
- **Redis**: Available on `localhost:6380`

## SSL Setup (Later)

After obtaining SSL certificates (Let's Encrypt recommended):

1. **Install Certbot:**
   ```bash
   sudo apt install certbot python3-certbot-nginx
   ```

2. **Obtain SSL certificate:**
   ```bash
   sudo certbot --nginx -d your-domain.com -d www.your-domain.com
   ```

3. **Auto-renewal:**
   ```bash
   sudo crontab -e
   # Add: 0 12 * * * /usr/bin/certbot renew --quiet
   ```

## Troubleshooting

- **Check Nginx status:** `sudo systemctl status nginx`
- **Check configuration:** `sudo nginx -t`
- **View logs:** `sudo tail -f /var/log/nginx/iduview_error.log`
- **Restart Nginx:** `sudo systemctl restart nginx`
