// backend\src\routes\upload.ts

import express, { Request, Response, NextFunction } from "express";
import path from "path";
import sharp from "sharp";
import fs from "fs/promises";
import { authenticate, authorize, AuthRequest } from "../middleware/auth";
import {
  uploadPanorama,
  uploadHotspotImage,
  uploadContentFile,
} from "../middleware/upload";
import { config } from "../utils/config";
import logger from "../utils/logger";

const router = express.Router();

// Helper function to get file info
const getFileInfo = async (filePath: string) => {
  const stats = await fs.stat(filePath);
  const metadata = await sharp(filePath).metadata();

  return {
    size: stats.size,
    width: metadata.width || 0,
    height: metadata.height || 0,
    format: metadata.format || "unknown",
  };
};

// POST /api/upload/panorama
router.post(
  "/panorama",
  [authenticate, authorize("admin")],
  (req: AuthRequest, res: Response, next: NextFunction) => {
    uploadPanorama.single("panorama")(req, res, async (err): Promise<void> => {
      if (err) {
        res.status(400).json({
          success: false,
          error: { message: err.message },
        });
        return;
      }

      try {
        if (!req.file) {
          res.status(400).json({
            success: false,
            error: { message: "No file uploaded" },
          });
          return;
        }

        const filePath = req.file.path;
        const fileInfo = await getFileInfo(filePath);

        // Validate panorama dimensions (should be approximately 2:1 ratio for equirectangular)
        const aspectRatio = fileInfo.width / fileInfo.height;
        if (Math.abs(aspectRatio - 2) > 0.3) {
          // Delete the uploaded file
          await fs.unlink(filePath);

          res.status(400).json({
            success: false,
            error: {
              message: `Invalid panorama format. Expected 2:1 aspect ratio for equirectangular panoramas. Your image has ${aspectRatio.toFixed(
                2
              )}:1 ratio (${fileInfo.width}×${
                fileInfo.height
              }). Please convert to equirectangular format with 2:1 ratio (e.g., 4096×2048).`,
              details: {
                currentRatio: aspectRatio,
                currentDimensions: {
                  width: fileInfo.width,
                  height: fileInfo.height,
                },
                requiredRatio: 2,
                suggestedDimensions: {
                  width: fileInfo.height * 2,
                  height: fileInfo.height,
                },
              },
            },
          });
          return;
        }

        // Generate optimized versions if needed
        const optimizedPath = path.join(
          path.dirname(filePath),
          `optimized_${req.file.filename}`
        );

        // Generate thumbnail for panorama list
        const thumbnailPath = path.join(
          path.dirname(filePath),
          `thumb_${req.file.filename}`
        );

        // Create a web-optimized version
        await sharp(filePath)
          .jpeg({ quality: 85, progressive: true })
          .toFile(optimizedPath);

        // Create thumbnail (16:9 aspect ratio for panorama preview)
        await sharp(filePath)
          .resize(320, 180, {
            fit: "cover",
            position: "center",
          })
          .jpeg({ quality: 80 })
          .toFile(thumbnailPath);

        const optimizedInfo = await getFileInfo(optimizedPath);
        const thumbnailInfo = await getFileInfo(thumbnailPath);

        logger.info(`Panorama uploaded: ${req.file.filename}`);

        res.json({
          success: true,
          data: {
            original: {
              filename: req.file.filename,
              originalName: req.file.originalname,
              path: `/uploads/${req.file.filename}`,
              size: fileInfo.size,
              width: fileInfo.width,
              height: fileInfo.height,
              mimeType: req.file.mimetype,
            },
            optimized: {
              filename: `optimized_${req.file.filename}`,
              path: `/uploads/optimized_${req.file.filename}`,
              size: optimizedInfo.size,
              width: optimizedInfo.width,
              height: optimizedInfo.height,
              mimeType: "image/jpeg",
            },
            thumbnail: {
              filename: `thumb_${req.file.filename}`,
              path: `/uploads/thumb_${req.file.filename}`,
              size: thumbnailInfo.size,
              width: thumbnailInfo.width,
              height: thumbnailInfo.height,
              mimeType: "image/jpeg",
            },
          },
        });
      } catch (error) {
        logger.error("Panorama upload error:", error);
        next(error);
      }
    });
  }
);

// POST /api/upload/hotspot-image
router.post(
  "/hotspot-image",
  [authenticate, authorize("admin")],
  (req: AuthRequest, res: Response, next: NextFunction) => {
    uploadHotspotImage.single("image")(req, res, async (err): Promise<void> => {
      if (err) {
        res.status(400).json({
          success: false,
          error: { message: err.message },
        });
        return;
      }

      try {
        if (!req.file) {
          res.status(400).json({
            success: false,
            error: { message: "No file uploaded" },
          });
          return;
        }

        const filePath = req.file.path;
        const fileInfo = await getFileInfo(filePath);

        // Generate thumbnail
        const thumbnailPath = path.join(
          path.dirname(filePath),
          `thumb_${req.file.filename}`
        );

        await sharp(filePath)
          .resize(300, 300, {
            fit: "inside",
            withoutEnlargement: true,
          })
          .jpeg({ quality: 80 })
          .toFile(thumbnailPath);

        const thumbnailInfo = await getFileInfo(thumbnailPath);

        logger.info(`Hotspot image uploaded: ${req.file.filename}`);

        res.json({
          success: true,
          data: {
            original: {
              filename: req.file.filename,
              originalName: req.file.originalname,
              path: `/uploads/${req.file.filename}`,
              size: fileInfo.size,
              width: fileInfo.width,
              height: fileInfo.height,
              mimeType: req.file.mimetype,
            },
            thumbnail: {
              filename: `thumb_${req.file.filename}`,
              path: `/uploads/thumb_${req.file.filename}`,
              size: thumbnailInfo.size,
              width: thumbnailInfo.width,
              height: thumbnailInfo.height,
              mimeType: "image/jpeg",
            },
          },
        });
      } catch (error) {
        logger.error("Hotspot image upload error:", error);
        next(error);
      }
    });
  }
);

// POST /api/upload/content-file
router.post(
  "/content-file",
  [authenticate, authorize("admin")],
  (req: AuthRequest, res: Response, next: NextFunction) => {
    uploadContentFile.single("image")(req, res, async (err): Promise<void> => {
      if (err) {
        res.status(400).json({
          success: false,
          error: { message: err.message },
        });
        return;
      }

      try {
        if (!req.file) {
          res.status(400).json({
            success: false,
            error: { message: "No file uploaded" },
          });
          return;
        }

        const filePath = req.file.path;
        let fileInfo;

        // Handle different file types
        if (req.file.mimetype === "application/pdf") {
          // For PDFs, just get basic file stats
          const stats = await fs.stat(filePath);
          fileInfo = {
            size: stats.size,
            width: 0,
            height: 0,
            format: "pdf",
          };
        } else {
          // For images, get detailed metadata
          fileInfo = await getFileInfo(filePath);
        }

        // Generate thumbnail for images only
        let thumbnailInfo = null;
        if (req.file.mimetype.startsWith("image/")) {
          const thumbnailPath = path.join(
            path.dirname(filePath),
            `thumb_${req.file.filename}`
          );

          await sharp(filePath)
            .resize(300, 300, {
              fit: "inside",
              withoutEnlargement: true,
            })
            .jpeg({ quality: 80 })
            .toFile(thumbnailPath);

          thumbnailInfo = await getFileInfo(thumbnailPath);
        }

        logger.info(`Content file uploaded: ${req.file.filename}`);

        const responseData: any = {
          original: {
            filename: req.file.filename,
            originalName: req.file.originalname,
            path: `/uploads/${req.file.filename}`,
            size: fileInfo.size,
            width: fileInfo.width,
            height: fileInfo.height,
            mimeType: req.file.mimetype,
          },
        };

        if (thumbnailInfo) {
          responseData.thumbnail = {
            filename: `thumb_${req.file.filename}`,
            path: `/uploads/thumb_${req.file.filename}`,
            size: thumbnailInfo.size,
            width: thumbnailInfo.width,
            height: thumbnailInfo.height,
            mimeType: "image/jpeg",
          };
        }

        res.json({
          success: true,
          data: responseData,
        });
      } catch (error) {
        logger.error("Content file upload error:", error);
        next(error);
      }
    });
  }
);

// DELETE /api/upload/:filename
router.delete(
  "/:filename",
  [authenticate, authorize("admin")],
  async (
    req: AuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { filename } = req.params;

      // Security check - ensure filename doesn't contain path traversal
      if (
        filename.includes("..") ||
        filename.includes("/") ||
        filename.includes("\\")
      ) {
        res.status(400).json({
          success: false,
          error: { message: "Invalid filename" },
        });
        return;
      }

      const filePath = path.join(__dirname, "..", "..", "uploads", filename);

      try {
        await fs.unlink(filePath);

        // Also try to delete related files (optimized, thumbnail)
        const optimizedPath = path.join(
          path.dirname(filePath),
          `optimized_${filename}`
        );
        const thumbnailPath = path.join(
          path.dirname(filePath),
          `thumb_${filename}`
        );

        try {
          await fs.unlink(optimizedPath);
        } catch (e) {
          // Ignore if file doesn't exist
        }

        try {
          await fs.unlink(thumbnailPath);
        } catch (e) {
          // Ignore if file doesn't exist
        }

        logger.info(`File deleted: ${filename}`);

        res.json({
          success: true,
          data: { message: "File deleted successfully" },
        });
      } catch (error) {
        if ((error as any).code === "ENOENT") {
          res.status(404).json({
            success: false,
            error: { message: "File not found" },
          });
          return;
        }
        throw error;
      }
    } catch (error) {
      next(error);
    }
  }
);

export default router;
